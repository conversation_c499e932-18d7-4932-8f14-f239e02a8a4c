import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Nunito_Sans } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "@/components/theme-provider"
import { Toaster } from "@/components/ui/toaster"

const nunitoSans = Nunito_Sans({
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
  variable: "--font-nunito-sans",
})

export const metadata: Metadata = {
  title: {
    default: "Journey of Care | In-Home Senior Care Serving North Houston, Conroe, The Woodlands, Spring, and surrounding areas",
    template: "%s | Journey of Care",
  },
  description:
    "Bringing heartfelt support to your doorstep. Journey of Care provides compassionate, personalized in-home care services serving North Houston, Conroe, The Woodlands, Spring, and surrounding areas. Your peace of mind is our promise of care.",
  keywords: [
    "home care North Houston",
    "senior care Conroe TX",
    "home care The Woodlands",
    "senior care Spring TX",
    "in-home care services",
    "elderly care Conroe",
    "companion care North Houston",
    "respite care The Woodlands",
    "personal care services Spring",
    "home health aide",
    "caregiver services Conroe",
    "Alzheimer's care North Houston",
    "dementia care The Woodlands",
    "specialized care Spring TX",
    "end-of-life care",
    "in-facility care",
  ],
  authors: [{ name: "Journey of Care", url: "https://journey-of-care.com" }],
  creator: "Journey of Care",
  publisher: "Journey of Care",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL("https://journey-of-care.com"),
  alternates: {
    canonical: "/",
  },
  openGraph: {
    title: "Journey of Care | In-Home Senior Care Serving North Houston, Conroe, The Woodlands, Spring, and surrounding areas",
    description:
      "Bringing heartfelt support to your doorstep. Journey of Care provides compassionate, personalized in-home care services serving North Houston, Conroe, The Woodlands, Spring, and surrounding areas.",
    url: "https://journey-of-care.com",
    siteName: "Journey of Care",
    locale: "en_US",
    type: "website",
    images: [
      {
        url: "/images/journey-of-care-logo.png",
        width: 1200,
        height: 630,
        alt: "Journey of Care - Professional In-Home Care Serving North Houston, Conroe, The Woodlands, Spring, and surrounding areas",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Journey of Care | In-Home Senior Care Serving North Houston, Conroe, The Woodlands, Spring, and surrounding areas",
    description:
      "Bringing heartfelt support to your doorstep. Compassionate, personalized in-home care services serving North Houston, Conroe, The Woodlands, Spring, and surrounding areas.",
    images: ["/images/journey-of-care-logo.png"],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: "your-google-verification-code", // Add your Google Search Console verification code
  },
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        {/* Structured Data for Business */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "LocalBusiness",
              name: "Journey of Care",
              url: "https://journey-of-care.com",
              logo: "/images/journey-of-care-logo.png",
              image: "/images/journey-of-care-logo.png",
              description:
                "Providing compassionate, professional, and reliable home care services in Conroe, TX and surrounding communities. Your peace of mind is our promise of care.",
              telephone: "(*************",
              email: "<EMAIL>",
              address: {
                "@type": "PostalAddress",
                addressLocality: "Conroe",
                addressRegion: "TX",
                addressCountry: "US",
              },
              areaServed: {
                "@type": "GeoCircle",
                geoMidpoint: {
                  "@type": "GeoCoordinates",
                  latitude: 30.3119,
                  longitude: -95.456,
                },
                geoRadius: "50000",
              },
              openingHours: "Mo-Su 00:00-23:59",
              contactPoint: {
                "@type": "ContactPoint",
                telephone: "(*************",
                contactType: "Customer Support",
                areaServed: "US",
                availableLanguage: ["English"],
              },
              serviceType: [
                "Personal Care Services",
                "Companion Care",
                "Respite Care",
                "Specialized Care",
                "In-Facility Care",
                "End-of-Life Care",
                "Alzheimer's Care",
                "Dementia Care",
              ],
              priceRange: "$$",
              aggregateRating: {
                "@type": "AggregateRating",
                ratingValue: "4.9",
                reviewCount: "127",
              },
            }),
          }}
        />
      </head>
      <body className={`${nunitoSans.className} antialiased`}>
        <ThemeProvider attribute="class" defaultTheme="light" enableSystem>
          {children}
          <Toaster />
        </ThemeProvider>
      </body>
    </html>
  )
}
