"use client"

import { useEffect, useState, useRef } from "react"
import Link from "next/link"
import Image from "next/image"
import Head from "next/head"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import {
  Heart,
  Users,
  Shield,
  ChevronRight,
  Phone,
  Mail,
  Send,
  Star,
  Quote,
  ArrowRight,
  Clock,
  Award,
  Share2,
  ThumbsUp,
  Gift,
  HeartHandshake,
  ClipboardList,
  Home,
} from "lucide-react"
import { useInView } from "react-intersection-observer"
import { cn } from "@/lib/utils"
import { useTheme } from "next-themes"
import Header from "@/components/header"
import HeroCarousel from "@/components/hero-carousel"
import Footer from "@/components/footer"
import NewsletterSignup from "@/components/newsletter-signup"
import LatestBlogPosts from "@/components/latest-blog-posts"

function Tiktok({ className = "" }) {
  return (
    <svg className={className} viewBox="0 0 48 48" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
      <path d="M33.6,14.5c-2-1.4-3.3-3.6-3.4-6.2h-5.7v23.6c0,2.4-1.9,4.3-4.3,4.3S16,34.3,16,31.9s1.9-4.3,4.3-4.3c0.6,0,1.1,0.1,1.6,0.3v-6c-0.5-0.1-1-0.1-1.6-0.1c-5.7,0-10.3,4.6-10.3,10.3S14.7,42,20.3,42s10.3-4.6,10.3-10.3v-9.6c1.8,1.3,4.1,2.1,6.5,2.1V18C36.4,18,34.8,16.5,33.6,14.5z" />
    </svg>
  )
}

function YouTubeIcon({ className = "h-5 w-5" }) {
  return (
    <svg className={className} viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
      <path
        fill="#FF0000"
        d="M43.6 14.2s-.4-3-1.6-4.3c-1.5-1.6-3.2-1.6-4-1.7-5.6-.4-14.1-.4-14.1-.4s-8.5 0-14.1.4c-.8.1-2.5.1-4 1.7C4.6 11.2 4.2 14.2 4.2 14.2S3.8 17.6 3.8 21v5.9c0 3.4.4 6.8.4 6.8s.4 3 1.6 4.3c1.5 1.6 3.5 1.5 4.4 1.7 3.2.3 13.5.4 13.5.4s8.5 0 14.1-.4c.8-.1 2.5-.1 4-1.7 1.2-1.3 1.6-4.3 1.6-4.3s.4-3.4.4-6.8V21c0-3.4-.4-6.8-.4-6.8z"
      />
      <path fill="#FFF" d="M20 31.5l12.2-7.5L20 16.5z" />
    </svg>
  )
}

function PinterestIcon({ className = "h-5 w-5" }) {
  return (
    <svg className={className} viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
      <circle cx="24" cy="24" r="22" fill="#E60023" />
      <path
        fill="#fff"
        d="M25.4 28.1c-.9 4.7-2 9.2-5 12.2-1-.7-1.3-2.1-1.5-3.2-.3-1.3-.6-2.6-.8-4-.2-1.1-.5-2.2-.8-3.3-.3-1.3-.1-2.2.6-3.1 1.3-1.6 1.8-3.3 1.9-5.4 0-3.2 2.2-5.6 5.4-5.6 3.1 0 4.6 2.3 4.6 5.1 0 3.1-1.4 5.7-3.4 5.7-1.1 0-1.9-.9-1.6-2.1.3-1.2.9-2.4.9-3.6 0-.8-.4-1.6-1.3-1.6-1 0-1.8 1-1.8 2.4 0 1 .3 1.7.3 1.7s-1 4.2-1.2 4.9c-.3 1.4-.2 3.3-.1 4.6.1.6.5.9 1 .5 1.2-1.4 2.2-3.5 2.5-5.2.2-.8 1.1-3.9 1.1-3.9.6 1 2.1 1.9 3.7 1.9 4.9 0 6.9-4.3 6.9-8 0-3.4-2.9-6.7-7.7-6.7-5.8.1-9.3 4.3-9.3 8.9 0 1.6.5 3 1.3 3.9.1.1.1.1.1.2-.1.4-.3 1.4-.3 1.6-.1.2-.2.3-.4.2-1.4-.7-2.2-2.7-2.2-4.3 0-3.6 2.8-7.8 8.4-7.8 4.4 0 7.8 3.1 7.8 7.3 0 4.4-2.7 8-6.5 8-1.3 0-2.6-.7-3-1.5l-.9 3.4z"
      />
    </svg>
  )
}

export default function HomePage() {
  const [scrolled, setScrolled] = useState(false)
  const [mounted, setMounted] = useState(false)
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const [videoPlaying, setVideoPlaying] = useState(true)
  const [videoMuted, setVideoMuted] = useState(true)
  const { setTheme, theme } = useTheme()
  const menuRef = useRef<HTMLDivElement>(null)
  const videoRef = useRef<HTMLVideoElement>(null)

  // Make sure the component is mounted before accessing theme
  useEffect(() => {
    setMounted(true)
  }, [])

  // Handle scroll effect for header
  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 20)
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  // Close menu when clicking outside
  useEffect(() => {
    if (!mobileMenuOpen) return

    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setMobileMenuOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    document.body.style.overflow = "hidden"

    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
      document.body.style.overflow = ""
    }
  }, [mobileMenuOpen])

  // Video controls
  const toggleVideoPlay = () => {
    if (videoRef.current) {
      if (videoPlaying) {
        videoRef.current.pause()
      } else {
        videoRef.current.play()
      }
      setVideoPlaying(!videoPlaying)
    }
  }

  const toggleVideoMute = () => {
    if (videoRef.current) {
      videoRef.current.muted = !videoMuted
      setVideoMuted(!videoMuted)
    }
  }

  // Simplified animation variants
  const fadeIn = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.6 } },
  }

  const fadeInLeft = {
    hidden: { opacity: 0, x: -20 },
    visible: { opacity: 1, x: 0, transition: { duration: 0.6 } },
  }

  const fadeInRight = {
    hidden: { opacity: 0, x: 20 },
    visible: { opacity: 1, x: 0, transition: { duration: 0.6 } },
  }

  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.6 } },
  }

  // Simplified intersection observer hooks
  const { ref: heroRef, inView: heroInView } = useInView({ triggerOnce: true, threshold: 0.1 })
  const { ref: servicesRef, inView: servicesInView } = useInView({ triggerOnce: true, threshold: 0.1 })
  const { ref: referRef, inView: referInView } = useInView({ triggerOnce: true, threshold: 0.1 })
  const { ref: betterChoiceRef, inView: betterChoiceInView } = useInView({ triggerOnce: true, threshold: 0.1 })
  const { ref: aboutRef, inView: aboutInView } = useInView({ triggerOnce: true, threshold: 0.1 })
  const { ref: missionRef, inView: missionInView } = useInView({ triggerOnce: true, threshold: 0.1 })
  const { ref: testimonialsRef, inView: testimonialsInView } = useInView({ triggerOnce: true, threshold: 0.1 })
  const { ref: contactRef, inView: contactInView } = useInView({ triggerOnce: true, threshold: 0.1 })
  const { ref: faqRef, inView: faqInView } = useInView({ triggerOnce: true, threshold: 0.1 })

  // Custom container class with increased margins on desktop
  const containerClass = "px-4 md:px-8 lg:px-16 xl:px-24 2xl:px-32 mx-auto max-w-[1600px]"

  // Updated services data for Journey of Care - Complete service offerings
  const services = [
    {
      id: "inclusive-care",
      title: "Inclusive Care for All Abilities",
      description:
        "We proudly support individuals with autism, intellectual disabilities, and neurodivergent needs. Our caregivers are trained in communication strategies and sensory-friendly care to build trust and create safe, empowering environments.",
      image: "in6.jpg",
      color: "#274166",
      icon: Heart,
    },
    {
      id: "personal-care",
      title: "Personal Care Services",
      description:
        "Respectful support for every stage of life. We provide personal care with heart, helping with daily routines that matter most — with warmth, dignity, and respect. Our care is never one-size-fits-all.",
      image: "in7.jpg",
      color: "#274166",
      icon: Home,
    },
    {
      id: "companion-care",
      title: "Companion Care",
      description:
        "Genuine connections. Meaningful moments. Our Companion Care services bring joy, engagement, and connection to everyday life — whether through conversation, hobbies, or simply having someone who truly listens.",
      image: "in8.jpg",
      color: "#f8d134",
      icon: Users,
    },
    {
      id: "respite-care",
      title: "Respite Care",
      description:
        "Because caregivers deserve care too. We provide trusted relief, whether you need a few hours, a few days, or something more regular. We step in with compassion and consistency.",
      image: "in9.jpg",
      color: "#6b7280",
      icon: HeartHandshake,
    },
    {
      id: "in-facility-care",
      title: "In-Facility Care",
      description:
        "Personal support that goes beyond the call light. Our in-facility care provides dedicated one-on-one support, working alongside staff to make sure your loved one feels seen, safe, and genuinely cared for.",
      image: "in10.jpg",
      color: "#e05691",
      icon: Shield,
    },
  ]

  // Contact information - Updated for Journey of Care
  const contactInfo = [
    {
      label: "Phone",
      value: "(*************",
      icon: Phone,
    },
    {
      label: "Email",
      value: "<EMAIL>",
      icon: Mail,
    },
  ]

  // Testimonials data
  const testimonials = [
    {
      quote:
        "My teenage son has special needs, and finding the right support has always been challenging. Journey of Care gets it. Their caregiver is patient, understanding, and really connects with him. It's been life-changing for our family.",
      author: "Michael L.",
      location: "North Houston, TX",
    },
    {
      quote:
        "When my husband was diagnosed with early-stage dementia, I felt overwhelmed. Journey of Care helped us create a routine that works for both of us. Their caregiver is like having a friend who truly cares.",
      author: "Jennifer S.",
      location: "Magnolia, TX",
    },
    {
      quote:
        "At 89, I thought I'd have to move to a facility, but Journey of Care made it possible for me to stay home. My caregiver doesn't just help with daily tasks—she's become a dear friend. We laugh together every day.",
      author: "Robert T.",
      location: "Tomball, TX",
    },
  ]

  // FAQ data - Updated for Journey of Care
  const faqs = [
    {
      question: "What home care services does Journey of Care offer?",
      answer:
        "We offer comprehensive home care services including personal care assistance with daily living activities, companion care for emotional support and social engagement, respite care for family caregivers, specialized services for clients with Alzheimer's or dementia, in-facility care, and end-of-life care. All our services are tailored to meet the unique needs of each client in Serving North Houston, Conroe, TheWoodlands, Spring, and surrounding areas",
    },
    {
      question: "How do I know if my loved one needs home care services?",
      answer:
        "Signs that indicate your loved one might need home care include difficulty with daily activities (bathing, dressing, meal preparation), medication management issues, mobility challenges, social isolation, or a recent hospital stay. We offer free in-home assessments to evaluate needs and determine the most appropriate care plan.",
    },
    {
      question: "Are your caregivers licensed and insured?",
      answer:
        "Yes, all our caregivers are certified, insured, and undergo comprehensive background checks. Our team receives ongoing training to ensure they provide the highest quality of care. We are fully compliant with all state and federal regulations governing home care providers in Texas.",
    },
    {
      question: "What makes Journey of Care different?",
      answer:
        "We are dedicated to providing trustworthy, heartfelt care with a reputation built on reliability. Our personalized approach ensures that each care plan is tailored to the individual's specific needs and preferences. We treat every client with compassion and respect, offering steady hands and open hearts every day.",
    },
    {
      question: "What are your rates and do you accept insurance?",
      answer:
        "Our rates vary depending on the type and level of care needed. We accept long-term care insurance, private pay, and work with families to explore all payment options to ensure you receive the care you need. Contact us for a free consultation to discuss pricing and payment options.",
    },
    {
      question: "How quickly can home care services start?",
      answer:
        "In most cases, we can initiate services within 24-48 hours of an initial assessment. For urgent situations, we strive to provide care as quickly as possible. The timeline depends on the complexity of care needed and scheduling availability.",
    },
    {
      question: "What areas do you serve?",
      answer:
        "We proudly serve Serving North Houston, Conroe, TheWoodlands, Spring, and surrounding areas Please contact us for specific information about service availability in your location.",
    },
  ]

  // Core values data
  const coreValues = [
    {
      icon: Heart,
      title: "Compassion",
      description: "We approach every client with genuine care, empathy, and understanding of their unique situation.",
    },
    {
      icon: Shield,
      title: "Trustworthy Care",
      description: "We uphold the highest standards of reliability and professionalism in all our care delivery.",
    },
    {
      icon: Award,
      title: "Personalized Service",
      description: "We create customized care plans designed around your specific needs and preferences.",
    },
    {
      icon: Users,
      title: "Family-Centered Approach",
      description: "We honor the trust placed in us by providing respectful, dignified care for your loved ones.",
    },
  ]

  // Trust section benefits
  const trustBenefits = [
    {
      icon: HeartHandshake,
      title: "Trustworthy and Compassionate Caregivers",
      description: "Certified caregivers who provide reliable, heartfelt support with steady hands and open hearts.",
    },
    {
      icon: ClipboardList,
      title: "Customized Care Plans",
      description: "Personalized care plans tailored to each individual's unique needs and preferences.",
    },
    {
      icon: Clock,
      title: "24/7 Availability for Emergencies",
      description: "Round-the-clock availability to ensure peace of mind and support when you need it most.",
    },
    {
      icon: Award,
      title: "Serving with Dedication",
      description: "Committed to improving quality of life with care, professionalism, and dedication.",
    },
  ]

  return (
    <>
      <Head>
        <title>Journey of Care | Heartfelt Home Care Services in Conroe, TX</title>
        <meta
          name="description"
          content="Bringing heartfelt support to your doorstep. Journey of Care provides personalized home care services in Conroe, TX including personal care, companion care, respite care, and specialized services."
        />
        <meta
          name="keywords"
          content="home care Conroe TX, personal care services, companion care, respite care, specialized care, Alzheimer's care, dementia care, in-home care"
        />
        <link rel="canonical" href="https://journey-of-care.com/" />
      </Head>

      <div className="min-h-screen font-sans bg-white dark:bg-gray-950 overflow-x-hidden overflow-y-hidden max-w-[100vw] transition-colors duration-300">
        {/* Gradient Orbs */}
        <div className="fixed inset-0 overflow-hidden pointer-events-none z-0 max-w-[100vw]">
          <div className="absolute -top-40 -right-40 w-96 h-96 rounded-full bg-gradient-to-br from-primary/10 to-primary-light/5 dark:from-primary/5 dark:to-primary-light/2 blur-3xl"></div>
          <div className="absolute top-1/3 -left-40 w-80 h-80 rounded-full bg-gradient-to-tr from-secondary/10 to-secondary-light/5 dark:from-secondary/5 dark:to-secondary-light/2 blur-3xl"></div>
          <div className="absolute bottom-0 right-1/4 w-72 h-72 rounded-full bg-gradient-to-r from-[#757575]/10 to-[#9e9e9e]/5 dark:from-[#757575]/5 dark:to-[#9e9e9e]/2 blur-3xl"></div>
        </div>

        <Header />

        {/* Hero Carousel */}
        <HeroCarousel />

        {/* Main Content with proper H1 */}
        <main>
          {/* Welcome Banner */}
          <section className="py-12 relative z-10 bg-gradient-to-b from-white to-[#f9f9f9] dark:from-gray-950 dark:to-gray-900 border-b border-gray-100 dark:border-gray-800">
            <div className={cn(containerClass)}>
              <div className="max-w-4xl mx-auto text-center">
                <h1 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-4">
                  Bringing Heartfelt Support to Your Doorstep
                </h1>
                <p className="text-xl text-gray-700 dark:text-gray-300 leading-relaxed mb-6">
                  Your Peace of Mind Is Our Promise of Care
                </p>
                <div className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed mb-6 space-y-4">
                  <p>
                    At Journey of Care, we believe everyone deserves to be treated like family — with patience, kindness, and real attention. Our mission is to bring comfort, connection, and peace of mind into the everyday lives of the people we serve.
                  </p>
                  <p>
                    Whether it's preparing a meal, offering a listening ear, or helping with daily tasks, we show up with intention — because care isn't just a service, it's a relationship.
                  </p>
                  <p>
                    Serving North Houston, Conroe, The Woodlands, Spring, and surrounding areas, we're a nurse-owned agency built on compassion, professionalism, and deep respect for every family we serve. At Journey of Care, every journey begins with care — and continues with trust.
                  </p>
                </div>

                <div className="mt-8">
                  <Link href="/consultation">
                    <Button className="rounded-full bg-gradient-to-r from-pink to-lavender hover:from-pink-dark hover:to-lavender-dark text-white px-8 py-3 h-auto transition-all duration-300 hover:shadow-lg flex items-center gap-2 group mx-auto">
                      <span>Schedule Your Free In-Home Assessment</span>
                      <ArrowRight className="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
                    </Button>
                  </Link>
                  <p className="text-gray-600 dark:text-gray-300 mt-4">
                    Call us at{" "}
                    <a href="tel:+18324460705" className="text-primary hover:underline font-medium">
                      (*************
                    </a>{" "}
                    or contact us at{" "}
                    <a href="mailto:<EMAIL>" className="text-primary hover:underline font-medium">
                      <EMAIL>
                    </a>{" "}
                    to get started.
                  </p>
                </div>
              </div>
            </div>
          </section>

          {/* Inclusive Care Section */}
          <section className="py-16 relative z-10 bg-gradient-to-b from-[#f9f9f9] to-white dark:from-gray-900 dark:to-gray-950 border-b border-gray-100 dark:border-gray-800">
            <div className={cn(containerClass)}>
              <div className="max-w-4xl mx-auto text-center">
                <div className="inline-block">
                  <Badge className="px-4 py-1.5 bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary-light rounded-full text-sm font-medium mb-4">
                    Inclusive Care
                  </Badge>
                </div>
                <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">
                  Care for All Abilities
                </h2>
                <div className="w-20 h-1 bg-primary mx-auto rounded-full mb-8"></div>
                <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed mb-6">
                  We proudly support individuals with autism, intellectual disabilities, and neurodivergent needs alongside older adults and their families. Our caregivers are trained in communication strategies and sensory-friendly care to build trust and create safe, empowering environments for every client.
                </p>

                {/* Key Features */}
                <div className="grid md:grid-cols-3 gap-6 mt-8">
                  <div className="flex flex-col items-center text-center">
                    <div className="w-12 h-12 bg-primary/10 dark:bg-primary/20 rounded-full flex items-center justify-center mb-3">
                      <Heart className="h-6 w-6 text-primary dark:text-primary-light" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-800 dark:text-gray-100 mb-2">Autism Support</h3>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">Specialized care for individuals with autism spectrum disorders</p>
                  </div>
                  <div className="flex flex-col items-center text-center">
                    <div className="w-12 h-12 bg-primary/10 dark:bg-primary/20 rounded-full flex items-center justify-center mb-3">
                      <Users className="h-6 w-6 text-primary dark:text-primary-light" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-800 dark:text-gray-100 mb-2">Neurodivergent Care</h3>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">Understanding and support for all neurological differences</p>
                  </div>
                  <div className="flex flex-col items-center text-center">
                    <div className="w-12 h-12 bg-primary/10 dark:bg-primary/20 rounded-full flex items-center justify-center mb-3">
                      <Shield className="h-6 w-6 text-primary dark:text-primary-light" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-800 dark:text-gray-100 mb-2">Safe Environments</h3>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">Creating empowering, trust-based care environments</p>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Services Section */}
          <section
            ref={servicesRef}
            className="py-24 relative z-10 overflow-hidden bg-gradient-to-b from-[#f9f9f9] to-white dark:from-gray-900 dark:to-gray-950"
            id="services"
          >
            <motion.div
              initial="hidden"
              animate={servicesInView ? "visible" : "hidden"}
              variants={fadeIn}
              className={cn(containerClass)}
            >
              <div className="text-center mb-16">
                <div className="inline-block">
                  <Badge className="px-4 py-1.5 bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary-light rounded-full text-sm font-medium mb-4">
                    What We Offer
                  </Badge>
                </div>
                <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-4">
                  Comprehensive Home Care Services
                </h2>
                <div className="w-20 h-1 bg-primary mx-auto rounded-full mb-6"></div>
                <p className="max-w-2xl mx-auto text-gray-600 dark:text-gray-300 text-lg font-light">
                  Each service is delivered with care, professionalism, and a commitment to improving quality of life.
                </p>
              </div>

              {/* Service cards - Updated to 3 columns for 6 services */}
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
                {services.map((service, index) => (
                  <div key={index} className="group">
                    <Card className="h-full overflow-hidden border border-gray-100 dark:border-gray-800 shadow-md hover:shadow-xl transition-all duration-500 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl">
                      <div className="relative h-[250px] overflow-hidden">
                        <Image
                          src={service.image || "/placeholder.svg?height=250&width=400"}
                          alt={`${service.title} - Professional home care services`}
                          width={400}
                          height={250}
                          className="object-cover object-top w-full h-full transition-transform duration-500 group-hover:scale-105"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-70"></div>
                        <div className="absolute bottom-0 left-0 right-0 p-4">
                          <div className="flex items-center gap-2 mb-2">
                            <service.icon className="h-5 w-5 text-white" />
                            <h3 className="text-lg font-medium text-white group-hover:text-white transition-colors duration-300">
                              {service.title}
                            </h3>
                          </div>
                        </div>
                      </div>

                      <CardContent className="p-5 flex flex-col">
                        <p className="text-gray-600 dark:text-gray-300 font-light mb-4 flex-grow text-sm">
                          {service.description}
                        </p>

                        <div className="mt-auto">
                          <Link
                            href={`/services?service=${service.id}`}
                            className="inline-flex items-center text-sm font-medium text-primary dark:text-primary-light hover:text-primary-dark dark:hover:text-primary-light transition-colors duration-300"
                          >
                            Learn more about {service.title.toLowerCase()}
                            <ChevronRight className="ml-1 h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
                          </Link>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                ))}
              </div>

              <div className="mt-16 text-center">
                <h3 className="text-2xl font-light text-gray-800 dark:text-gray-100 mb-4">
                  Schedule Your Free In-Home Care Assessment Today!
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-6 max-w-2xl mx-auto">
                  Discover how we can help your loved one live independently and comfortably at home.
                </p>
                <div className="relative inline-block">
                  <Button className="rounded-full bg-gradient-to-r from-pink to-lavender hover:from-pink-dark hover:to-lavender-dark text-white px-8 py-6 h-auto transition-all duration-300 hover:shadow-lg">
                    <Link href="/consultation">Schedule a Free Assessment</Link>
                  </Button>
                </div>
              </div>
            </motion.div>

            {/* Decorative elements */}
            <div className="absolute top-20 right-0 w-64 h-64 bg-primary/20 dark:bg-primary/10 rounded-full blur-3xl -z-10"></div>
            <div className="absolute bottom-40 left-0 w-80 h-80 bg-secondary/20 dark:bg-secondary/10 rounded-full blur-3xl -z-10"></div>
          </section>

          {/* Trust Section */}
          <section className="py-16 relative z-10 bg-gradient-to-b from-white to-[#f9f9f9] dark:from-gray-950 dark:to-gray-900 border-b border-gray-100 dark:border-gray-800">
            <div className={cn(containerClass)}>
              <div className="max-w-4xl mx-auto text-center">
                <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">
                  Why Families Trust Us
                </h2>

                <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mt-8">
                  {trustBenefits.map((benefit, index) => (
                    <div
                      key={index}
                      className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm p-6 rounded-xl shadow-md"
                    >
                      <div className="w-12 h-12 rounded-full bg-gradient-to-br from-pink to-lavender mx-auto mb-4 flex items-center justify-center">
                        <benefit.icon className="h-6 w-6 text-white" />
                      </div>
                      <h3 className="text-lg font-medium mb-2">{benefit.title}</h3>
                      <p className="text-gray-600 dark:text-gray-300 text-sm">{benefit.description}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </section>

          {/* Refer Us Section */}
          <section
            ref={referRef}
            className="py-24 relative z-10 overflow-hidden bg-gradient-to-b from-white to-[#f9f9f9] dark:from-gray-950 dark:to-gray-900"
            id="refer"
          >
            <motion.div
              initial="hidden"
              animate={referInView ? "visible" : "hidden"}
              variants={fadeIn}
              className={cn(containerClass)}
            >
              <div className="text-center mb-16">
                <div className="inline-block">
                  <Badge className="px-4 py-1.5 bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary-light rounded-full text-sm font-medium mb-4">
                    Refer With Confidence
                  </Badge>
                </div>
                <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-4">
                  Share the Gift of Compassionate Care
                </h2>
                <div className="w-20 h-1 bg-primary dark:bg-primary-light mx-auto rounded-full mb-6"></div>
                <p className="max-w-2xl mx-auto text-gray-600 dark:text-gray-300 text-lg font-light">
                  Know someone who could benefit from our services? Your referral helps us extend our compassionate care
                  to more families in need.
                </p>
              </div>

              <div className="grid md:grid-cols-2 gap-12 items-center">
                <motion.div variants={fadeInLeft} className="relative w-full">
                  <div className="w-full h-0 pb-[75%] relative rounded-2xl overflow-hidden shadow-xl">
                    <Image
                      src="/in11.jpg"
                      alt="Caregiver helping senior"
                      fill
                      className="object-cover object-top rounded-2xl"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/30 to-transparent"></div>
                    <div className="absolute bottom-0 left-0 right-0 p-6">
                      <p className="text-white text-lg font-medium">Making a difference in someone's life</p>
                    </div>
                  </div>

                  <div className="absolute -bottom-10 -right-10 md:bottom-auto md:top-1/2 md:-translate-y-1/2 md:-right-16 w-32 h-32 rounded-full border-8 border-white dark:border-gray-800 shadow-xl overflow-hidden">
                    <div className="w-full h-full bg-secondary-light dark:bg-secondary/20 flex items-center justify-center">
                      <Share2 className="h-12 w-12 text-primary" />
                    </div>
                  </div>
                </motion.div>

                <motion.div variants={fadeInRight} className="space-y-6">
                  <h3 className="text-2xl font-medium text-gray-800 dark:text-gray-100">
                    Why Refer to Journey of Care?
                  </h3>

                  <div className="space-y-4">
                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 rounded-full bg-primary/10 dark:bg-primary/20 flex items-center justify-center flex-shrink-0">
                        <ThumbsUp className="h-6 w-6 text-primary dark:text-primary-light" />
                      </div>
                      <div>
                        <h4 className="text-lg font-medium text-gray-800 dark:text-gray-100">Trusted Quality Care</h4>
                        <p className="text-gray-600 dark:text-gray-300">
                          Our caregivers are thoroughly vetted, certified, and supervised to provide exceptional care.
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 rounded-full bg-secondary-light dark:bg-secondary/20 flex items-center justify-center flex-shrink-0">
                        <Heart className="h-6 w-6 text-primary" />
                      </div>
                      <div>
                        <h4 className="text-lg font-medium text-gray-800 dark:text-gray-100">Compassionate Approach</h4>
                        <p className="text-gray-600 dark:text-gray-300">
                          We treat each client with dignity, respect, and genuine care, focusing on their unique needs.
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 rounded-full bg-neutral dark:bg-gray-700 flex items-center justify-center flex-shrink-0">
                        <Gift className="h-6 w-6 text-gray-600 dark:text-gray-300" />
                      </div>
                      <div>
                        <h4 className="text-lg font-medium text-gray-800 dark:text-gray-100">Referral Appreciation</h4>
                        <p className="text-gray-600 dark:text-gray-300">
                          We value your trust and confidence in our services. Ask about our referral appreciation
                          program.
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="pt-6">
                    <Link href="/refer">
                      <Button className="rounded-full bg-gradient-to-r from-pink to-lavender hover:from-pink-dark hover:to-lavender-dark text-white px-6 py-2 h-auto transition-all duration-300 hover:shadow-lg flex items-center gap-2 group">
                        <span>Refer Someone Today</span>
                        <ArrowRight className="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
                      </Button>
                    </Link>
                  </div>
                </motion.div>
              </div>
            </motion.div>

            {/* Decorative elements */}
            <div className="absolute top-20 right-20 w-64 h-64 bg-primary/20 dark:bg-primary/10 rounded-full blur-3xl -z-10"></div>
            <div className="absolute bottom-40 left-20 w-80 h-80 bg-secondary/20 dark:bg-secondary/10 rounded-full blur-3xl -z-10"></div>
          </section>

          {/* About Us Section */}
          <section
            ref={aboutRef}
            className="py-24 relative z-10 overflow-hidden bg-gradient-to-b from-white to-[#f9f9f9] dark:from-gray-950 dark:to-gray-900"
            id="about"
          >
            <motion.div
              initial="hidden"
              animate={aboutInView ? "visible" : "hidden"}
              variants={fadeIn}
              className={cn(containerClass)}
            >
              <div className="text-center mb-16">
                <div className="inline-block">
                  <Badge className="px-4 py-1.5 bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary-light rounded-full text-sm font-medium mb-4">
                    About Us
                  </Badge>
                </div>
                <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-4">
                  Excellence in Home Healthcare
                </h2>
                <div className="w-20 h-1 bg-primary mx-auto rounded-full mb-6"></div>
                <p className="max-w-2xl mx-auto text-gray-600 dark:text-gray-300 text-lg font-light">
                  Journey of Care is a nurse-owned home care agency serving North Houston and surrounding communities, built on compassion, professionalism, and deep respect for the families we serve.
                </p>
              </div>

              <div className="grid md:grid-cols-2 gap-12 items-center">
                <motion.div variants={fadeInLeft} className="w-full">
                  <div className="w-full h-0 pb-[75%] relative rounded-2xl overflow-hidden shadow-xl">
                    <Image
                      src="/in12.jpg"
                      alt="Journey of Care Team - Professional caregivers"
                      fill
                      className="object-cover rounded-2xl"
                    />
                    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-6">
                      <p className="text-white text-lg font-medium">Our dedicated team of caregivers</p>
                    </div>
                  </div>
                </motion.div>

                <motion.div variants={fadeInRight} className="space-y-6">
                  <h3 className="text-2xl font-medium text-gray-800 dark:text-gray-100">Nurse-Owned & Operated</h3>

                  <p className="text-gray-600 dark:text-gray-300">
                    Journey of Care is a nurse-owned home care agency serving North Houston and surrounding communities. Founded by a registered nurse, our team is built on compassion, professionalism, and a deep respect for the families we serve. We provide dependable in-home support tailored to each individual's needs — including care for older adults, individuals with special needs, and neurodivergent clients. Our services are rooted in dignity, trust, and a belief that everyone deserves to feel safe and valued in their own home.
                  </p>

                  <div className="space-y-4 pt-4">
                    {coreValues.map((value, index) => (
                      <div key={index} className="flex items-start gap-3">
                        <div className="w-10 h-10 rounded-full bg-primary/10 dark:bg-primary/20 flex items-center justify-center flex-shrink-0">
                          <value.icon className="h-5 w-5 text-primary dark:text-primary-light" />
                        </div>
                        <div>
                          <h4 className="text-base font-medium text-gray-800 dark:text-gray-100">{value.title}</h4>
                          <p className="text-sm text-gray-600 dark:text-gray-300">{value.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="pt-6">
                    <Link href="/consultation">
                      <Button className="rounded-full bg-gradient-to-r from-pink to-lavender hover:from-pink-dark hover:to-lavender-dark text-white px-6 py-2 h-auto transition-all duration-300 hover:shadow-lg flex items-center gap-2 group">
                        <span>Learn More About Us</span>
                        <ArrowRight className="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
                      </Button>
                    </Link>
                  </div>
                </motion.div>
              </div>
            </motion.div>

            {/* Decorative elements */}
            <div className="absolute top-20 left-20 w-64 h-64 bg-primary/20 dark:bg-primary/10 rounded-full blur-3xl -z-10"></div>
            <div className="absolute bottom-40 right-20 w-80 h-80 bg-secondary/20 dark:bg-secondary/10 rounded-full blur-3xl -z-10"></div>
          </section>

          {/* Testimonials Section */}
          <section
            ref={testimonialsRef}
            className="py-24 relative z-10 overflow-hidden bg-gradient-to-b from-[#f9f9f9] to-white dark:from-gray-900 dark:to-gray-950"
            id="testimonials"
          >
            <motion.div
              initial="hidden"
              animate={testimonialsInView ? "visible" : "hidden"}
              variants={fadeIn}
              className={cn(containerClass)}
            >
              <div className="text-center mb-16">
                <div className="inline-block">
                  <Badge className="px-4 py-1.5 bg-secondary-light dark:bg-secondary/20 text-primary rounded-full text-sm font-medium mb-4">
                    Testimonials
                  </Badge>
                </div>
                <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-4">
                  What Our Clients Say
                </h2>
                <div className="w-20 h-1 bg-primary dark:bg-primary-light mx-auto rounded-full mb-6"></div>
                <p className="max-w-2xl mx-auto text-gray-600 dark:text-gray-300 text-lg font-light">
                  Read stories from families who have experienced the Journey of Care difference.
                </p>
              </div>

              <div className="grid md:grid-cols-3 gap-8">
                {testimonials.map((testimonial, index) => (
                  <Card
                    key={index}
                    className="border border-gray-100 dark:border-gray-800 shadow-md hover:shadow-xl transition-all duration-300 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl"
                  >
                    <CardContent className="p-6">
                      <div className="flex items-center mb-4">
                        <Quote className="h-5 w-5 text-primary mr-2" />
                        <h3 className="text-lg font-medium text-gray-800 dark:text-gray-100">Client Feedback</h3>
                      </div>
                      <p className="text-gray-600 dark:text-gray-300 font-light mb-4">{testimonial.quote}</p>
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-primary dark:text-primary-light">
                            {testimonial.author}
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-400">{testimonial.location}</p>
                        </div>
                        <Star className="h-5 w-5 text-yellow-500" />
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </motion.div>
          </section>

          {/* Why Choose Us Section */}
          <section className="py-16 relative z-10 bg-gradient-to-b from-white to-[#f9f9f9] dark:from-gray-950 dark:to-gray-900">
            <div className={cn(containerClass)}>
              <div className="max-w-4xl mx-auto text-center">
                <div className="inline-block">
                  <Badge className="px-4 py-1.5 bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary-light rounded-full text-sm font-medium mb-4">
                    Why Choose Journey of Care
                  </Badge>
                </div>
                <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">
                  Your Trusted Partner in Home Care
                </h2>
                <div className="w-20 h-1 bg-primary mx-auto rounded-full mb-8"></div>

                <div className="grid md:grid-cols-3 gap-8 mt-12">
                  <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm p-6 rounded-xl shadow-md border border-gray-100 dark:border-gray-700">
                    <div className="w-16 h-16 rounded-full bg-gradient-to-br from-pink to-lavender mx-auto mb-4 flex items-center justify-center">
                      <Shield className="h-8 w-8 text-white" />
                    </div>
                    <h3 className="text-xl font-medium text-gray-800 dark:text-gray-100 mb-3">Licensed & Insured</h3>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      All our caregivers are certified, licensed, and fully insured for your peace of mind.
                    </p>
                  </div>

                  <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm p-6 rounded-xl shadow-md border border-gray-100 dark:border-gray-700">
                    <div className="w-16 h-16 rounded-full bg-gradient-to-br from-pink to-lavender mx-auto mb-4 flex items-center justify-center">
                      <Heart className="h-8 w-8 text-white" />
                    </div>
                    <h3 className="text-xl font-medium text-gray-800 dark:text-gray-100 mb-3">Compassionate Care</h3>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      We provide heartfelt, personalized care that treats each client with dignity and respect.
                    </p>
                  </div>

                  <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm p-6 rounded-xl shadow-md border border-gray-100 dark:border-gray-700">
                    <div className="w-16 h-16 rounded-full bg-gradient-to-br from-pink to-lavender mx-auto mb-4 flex items-center justify-center">
                      <Clock className="h-8 w-8 text-white" />
                    </div>
                    <h3 className="text-xl font-medium text-gray-800 dark:text-gray-100 mb-3">24/7 Availability</h3>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Round-the-clock support and emergency availability when you need us most.
                    </p>
                  </div>
                </div>

                <div className="mt-12">
                  <Link href="/consultation">
                    <Button className="rounded-full bg-gradient-to-r from-pink to-lavender hover:from-pink-dark hover:to-lavender-dark text-white px-8 py-3 h-auto transition-all duration-300 hover:shadow-lg flex items-center gap-2 group mx-auto">
                      <span>Start Your Care Journey Today</span>
                      <ArrowRight className="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </section>

          {/* Payment Policy Section */}
          <section className="py-16 relative z-10 bg-gradient-to-b from-[#f9f9f9] to-white dark:from-gray-900 dark:to-gray-950 border-b border-gray-100 dark:border-gray-800">
            <div className={cn(containerClass)}>
              <div className="max-w-4xl mx-auto text-center">
                <div className="inline-block">
                  <Badge className="px-4 py-1.5 bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary-light rounded-full text-sm font-medium mb-4">
                    Payment Information
                  </Badge>
                </div>
                <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">
                  Private Pay Only
                </h2>
                <div className="w-20 h-1 bg-primary mx-auto rounded-full mb-8"></div>
                <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed">
                  Journey of Care accepts all major credit and debit cards through secure online billing. We do not accept checks, cash, or insurance plans.
                </p>

                {/* Payment Methods Icons */}
                <div className="mt-8 flex justify-center items-center gap-6 flex-wrap">
                  <div className="flex items-center gap-2 text-gray-600 dark:text-gray-300">
                    <div className="w-8 h-8 bg-primary/10 dark:bg-primary/20 rounded-lg flex items-center justify-center">
                      <svg className="w-5 h-5 text-primary" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M2 4h20v16H2V4zm2 2v12h16V6H4zm2 2h12v2H6V8zm0 4h8v2H6v-2z"/>
                      </svg>
                    </div>
                    <span className="text-sm font-medium">Credit Cards</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-600 dark:text-gray-300">
                    <div className="w-8 h-8 bg-primary/10 dark:bg-primary/20 rounded-lg flex items-center justify-center">
                      <svg className="w-5 h-5 text-primary" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M2 4h20v16H2V4zm2 2v12h16V6H4zm2 2h12v2H6V8zm0 4h8v2H6v-2z"/>
                      </svg>
                    </div>
                    <span className="text-sm font-medium">Debit Cards</span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-600 dark:text-gray-300">
                    <div className="w-8 h-8 bg-primary/10 dark:bg-primary/20 rounded-lg flex items-center justify-center">
                      <Shield className="w-5 h-5 text-primary" />
                    </div>
                    <span className="text-sm font-medium">Secure Online Billing</span>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Latest Blog Posts Section */}
          <LatestBlogPosts />

          {/* Newsletter Signup Section */}
          <NewsletterSignup />

          {/* Contact Section */}
          <section
            ref={contactRef}
            className="py-24 relative z-10 overflow-hidden bg-gradient-to-b from-[#f9f9f9] to-white dark:from-gray-900 dark:to-gray-950"
            id="contact"
          >
            <motion.div
              initial="hidden"
              animate={contactInView ? "visible" : "hidden"}
              variants={fadeIn}
              className={cn(containerClass)}
            >
              <div className="text-center mb-16">
                <div className="inline-block">
                  <Badge className="px-4 py-1.5 bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary-light rounded-full text-sm font-medium mb-4">
                    Contact Us
                  </Badge>
                </div>
                <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-4">
                  Get in Touch with Journey of Care
                </h2>
                <div className="w-20 h-1 bg-primary mx-auto rounded-full mb-6"></div>
                <p className="max-w-2xl mx-auto text-gray-600 dark:text-gray-300 text-lg font-light">
                  We're here to answer your questions and help you find the right home care solution.
                </p>
              </div>

              <div className="grid md:grid-cols-2 gap-12 items-center">
                <div className="relative rounded-xl overflow-hidden shadow-lg">
                  <div className="w-full h-0 pb-[75%] relative">
                    <Image
                      src="/1n13.jpg"
                      alt="Journey of Care team"
                      fill
                      className="object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-70"></div>
                    <div className="absolute bottom-0 left-0 right-0 p-4">
                      <p className="text-white text-lg font-medium">Professional home care services</p>
                    </div>
                  </div>
                </div>

                <div className="space-y-6">
                  <h3 className="text-2xl font-medium text-gray-800 dark:text-gray-100">
                    Let's Discuss Your Home Care Needs
                  </h3>

                  <p className="text-gray-600 dark:text-gray-300">
                    Contact us today to schedule a free in-home consultation and learn how Journey of Care can
                    provide the support and care you need in Serving North Houston, Conroe, TheWoodlands, Spring, and surrounding areas
                  </p>

                  <div className="space-y-4">
                    {contactInfo.map((item, index) => (
                      <div key={index} className="flex items-center gap-3">
                        <item.icon className="h-5 w-5 text-primary dark:text-primary-light" />
                        <a
                          href={item.label === "Phone" ? `tel:${item.value}` : `mailto:${item.value}`}
                          className="text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary-light transition-colors duration-300"
                        >
                          {item.value}
                        </a>
                      </div>
                    ))}
                  </div>

                  <div className="pt-6">
                    <Link href="/contact">
                      <Button className="rounded-full bg-gradient-to-r from-pink to-lavender hover:from-pink-dark hover:to-lavender-dark text-white px-6 py-2 h-auto transition-all duration-300 hover:shadow-lg flex items-center gap-2 group">
                        <span>Send us a Message</span>
                        <Send className="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>
            </motion.div>
          </section>

          {/* FAQ Section */}
          <section
            ref={faqRef}
            className="py-24 relative z-10 overflow-hidden bg-gradient-to-b from-[#f9f9f9] to-white dark:from-gray-900 dark:to-gray-950"
            id="faq"
          >
            <motion.div
              initial="hidden"
              animate={faqInView ? "visible" : "hidden"}
              variants={fadeIn}
              className={cn(containerClass)}
            >
              <div className="text-center mb-16">
                <div className="inline-block">
                  <Badge className="px-4 py-1.5 bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary-light rounded-full text-sm font-medium mb-4">
                    FAQ
                  </Badge>
                </div>
                <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-4">
                  Frequently Asked Questions About Our Home Care Services
                </h2>
                <div className="w-20 h-1 bg-primary mx-auto rounded-full mb-6"></div>
                <p className="max-w-2xl mx-auto text-gray-600 dark:text-gray-300 text-lg font-light">
                  Find answers to common questions about our home care services and care approach.
                </p>
              </div>

              <div className="space-y-6">
                {faqs.map((faq, index) => (
                  <Card
                    key={index}
                    className="border border-gray-100 dark:border-gray-800 shadow-md hover:shadow-xl transition-all duration-300 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl"
                  >
                    <CardContent className="p-6">
                      <h3 className="text-lg font-medium text-gray-800 dark:text-gray-100 mb-2">{faq.question}</h3>
                      <p className="text-gray-600 dark:text-gray-300 font-light">{faq.answer}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </motion.div>
          </section>
        </main>

        <Footer />
      </div>
    </>
  )
}
