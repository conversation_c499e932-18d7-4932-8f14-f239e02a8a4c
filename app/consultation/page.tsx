"use client"

import type React from "react"

import { useState } from "react"
import Link from "next/link"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { ArrowLeft, CalendarIcon, Send } from "lucide-react"
import { format } from "date-fns"
import { cn } from "@/lib/utils"
import Header from "@/components/header"
import { Gift } from "lucide-react"
import PageHero from "@/components/page-hero"
import Footer from "@/components/footer"

// Services data for checkbox selection - Updated for Journey of Care
const services = [
  {
    id: "personal-care",
    name: "Personal Care Services",
  },
  {
    id: "companion-care",
    name: "Companion Care",
  },
  {
    id: "respite-care",
    name: "Respite Care",
  },
  {
    id: "in-facility-care",
    name: "In-Facility Care",
  },
  {
    id: "specialized-care",
    name: "Specialized Care",
  },
  {
    id: "end-of-life-care",
    name: "End-of-Life Care",
  },
]

// Time slots for consultation
const timeSlots = ["9:00 AM", "10:00 AM", "11:00 AM", "1:00 PM", "2:00 PM", "3:00 PM", "4:00 PM"]

export default function ConsultationPage() {
  const [date, setDate] = useState<Date>()
  const [timeSlot, setTimeSlot] = useState<string>("")
  const [selectedServices, setSelectedServices] = useState<string[]>([])
  const [formSubmitted, setFormSubmitted] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submissionError, setSubmissionError] = useState<string | null>(null)
  const [smsConsent, setSmsConsent] = useState(false)

  const handleServiceChange = (serviceId: string, checked: boolean) => {
    if (checked) {
      setSelectedServices([...selectedServices, serviceId])
    } else {
      setSelectedServices(selectedServices.filter((id) => id !== serviceId))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setSubmissionError(null)

    // Get form data
    const form = e.target as HTMLFormElement
    const formData = new FormData(form)

    // Convert FormData to object
    const formValues: Record<string, string> = {}
    formData.forEach((value, key) => {
      formValues[key] = value.toString()
    })

    // Add SMS consent to form data
    formValues.smsConsent = smsConsent.toString()

    // Debug log to see what's being collected
    console.log("Form values:", formValues)

    try {
      // Send form data to our API
      const response = await fetch("/api/send-email", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: formValues.name,
          email: formValues.email,
          phone: formValues.phone || "Not provided",
          preferredDate: date ? format(date, "PPP") : "Not specified",
          preferredTime: timeSlot || "Not specified",
          servicesInterested: selectedServices.join(", ") || "Not specified",
          additionalInfo: formValues.additionalInfo || "Not provided",
          smsConsent: formValues.smsConsent,
          formType: "consultation",
        }),
      })

      const result = await response.json()

      if (response.ok) {
        setFormSubmitted(true)
        form.reset()
        setDate(undefined)
        setTimeSlot("")
        setSelectedServices([])
        setSmsConsent(false)
      } else {
        console.error("API error:", result)
        setSubmissionError(result.error || "There was a problem scheduling your consultation. Please try again.")
      }
    } catch (error) {
      console.error("Error submitting form:", error)
      setSubmissionError("There was a problem scheduling your consultation. Please try again.")
    } finally {
      setIsSubmitting(false)
    }
  }

  // Animation variants
  const fadeIn = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.6 } },
  }

  const containerClass = "px-4 md:px-8 lg:px-16 xl:px-24 2xl:px-32 mx-auto max-w-[1600px]"

  return (
    <div className="min-h-screen font-sans bg-white dark:bg-gray-950 overflow-hidden transition-colors duration-300">
      {/* Gradient Orbs */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
        <div className="absolute -top-40 -right-40 w-96 h-96 rounded-full bg-gradient-to-br from-primary/10 to-primary-light/5 dark:from-primary/5 dark:to-primary-light/2 blur-3xl"></div>
        <div className="absolute top-1/3 -left-40 w-80 h-80 rounded-full bg-gradient-to-tr from-secondary/10 to-secondary-light/5 dark:from-secondary/5 dark:to-secondary-light/2 blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-72 h-72 rounded-full bg-gradient-to-r from-[#757575]/10 to-[#9e9e9e]/5 dark:from-[#757575]/5 dark:to-[#9e9e9e]/2 blur-3xl"></div>
      </div>

      <Header />

      {/* Hero Section */}
      <PageHero
        title="Schedule Your Free Consultation"
        subtitle="Let's discuss how we can help with your home care needs"
        imageSrc="https://res.cloudinary.com/dvauarkh6/image/upload/v1747926314/pexels-olly-789822_1_vn9fiz.jpg"
        imageAlt="Home care professional consulting with client"
      />

      {/* Main Content */}
      <main className="py-16 relative z-10">
        <div className={containerClass}>
          <div className="mb-8">
            <Link
              href="/"
              className="inline-flex items-center text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary-light transition-colors duration-200"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Home
            </Link>
          </div>

          <motion.div initial="hidden" animate="visible" variants={fadeIn} className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <Badge className="px-4 py-1.5 bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary-light rounded-full text-sm font-medium mb-4">
                Free Consultation
              </Badge>
              <h1 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">
                Schedule Your Free In-Home Care Assessment with Journey of Care
              </h1>
              <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                Take the first step towards quality home care in Conroe, TX. Our free consultation allows us to
                understand your needs and explain how our services can help you or your loved one live comfortably at
                home.
              </p>
            </div>

            {/* Benefits Section */}
            <div className="grid md:grid-cols-3 gap-6 mb-12">
              <Card className="border border-gray-100 dark:border-gray-800 shadow-md bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl">
                <CardContent className="p-6 text-center">
                  <div className="w-16 h-16 rounded-full bg-primary/10 dark:bg-primary/20 flex items-center justify-center mx-auto mb-4">
                    <Gift className="h-8 w-8 text-primary dark:text-primary-light" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-800 dark:text-gray-100 mb-2">Completely Free</h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    No cost, no obligation. Just an opportunity to learn about our home care services.
                  </p>
                </CardContent>
              </Card>

              <Card className="border border-gray-100 dark:border-gray-800 shadow-md bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl">
                <CardContent className="p-6 text-center">
                  <div className="w-16 h-16 rounded-full bg-secondary/10 dark:bg-secondary/20 flex items-center justify-center mx-auto mb-4">
                    <CalendarIcon className="h-8 w-8 text-secondary dark:text-secondary-light" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-800 dark:text-gray-100 mb-2">Flexible Scheduling</h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    We work around your schedule to find a convenient time for your consultation.
                  </p>
                </CardContent>
              </Card>

              <Card className="border border-gray-100 dark:border-gray-800 shadow-md bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl">
                <CardContent className="p-6 text-center">
                  <div className="w-16 h-16 rounded-full bg-[#757575]/10 dark:bg-[#757575]/20 flex items-center justify-center mx-auto mb-4">
                    <Send className="h-8 w-8 text-[#757575] dark:text-[#9e9e9e]" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-800 dark:text-gray-100 mb-2">Personalized Plan</h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    We'll create a customized care plan based on your specific needs and preferences.
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Consultation Form */}
            <Card className="overflow-hidden border border-gray-100 dark:border-gray-800 shadow-lg bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl">
              <CardContent className="p-8">
                <h2 className="text-2xl font-light text-gray-800 dark:text-gray-100 mb-6">
                  Schedule Your Consultation
                </h2>

                {formSubmitted ? (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5 }}
                    className="bg-primary/10 dark:bg-primary/20 border border-primary/20 dark:border-primary-light/20 rounded-lg p-6 text-center"
                  >
                    <Gift className="h-12 w-12 text-primary dark:text-primary-light mx-auto mb-4" />
                    <h3 className="text-xl font-medium text-primary dark:text-primary-light mb-2">
                      Consultation Scheduled!
                    </h3>
                    <p className="text-gray-700 dark:text-gray-300 mb-4">
                      Thank you for scheduling your free consultation with Journey of Care. We'll contact you within
                      24 hours to confirm your appointment and answer any questions you may have about our home care
                      services in Conroe, TX.
                    </p>
                    <Button
                      onClick={() => setFormSubmitted(false)}
                      variant="outline"
                      className="border-primary dark:border-primary-light text-primary dark:text-primary-light hover:bg-primary/10 dark:hover:bg-primary/20"
                    >
                      Schedule Another Consultation
                    </Button>
                  </motion.div>
                ) : (
                  <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Personal Information */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label htmlFor="name" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          Full Name *
                        </label>
                        <Input
                          id="name"
                          name="name"
                          required
                          className="mt-1 rounded-lg border-gray-200 dark:border-gray-700 focus:border-primary dark:focus:border-primary-light focus:ring-primary/10 dark:focus:ring-primary-light/10 transition-all duration-300 dark:bg-gray-800 dark:text-gray-100"
                        />
                      </div>

                      <div>
                        <label htmlFor="email" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          Email Address *
                        </label>
                        <Input
                          id="email"
                          name="email"
                          type="email"
                          required
                          className="mt-1 rounded-lg border-gray-200 dark:border-gray-700 focus:border-primary dark:focus:border-primary-light focus:ring-primary/10 dark:focus:ring-primary-light/10 transition-all duration-300 dark:bg-gray-800 dark:text-gray-100"
                        />
                      </div>
                    </div>

                    <div>
                      <label htmlFor="phone" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Phone Number *
                      </label>
                      <Input
                        id="phone"
                        name="phone"
                        type="tel"
                        required
                        className="mt-1 rounded-lg border-gray-200 dark:border-gray-700 focus:border-primary dark:focus:border-primary-light focus:ring-primary/10 dark:focus:ring-primary-light/10 transition-all duration-300 dark:bg-gray-800 dark:text-gray-100"
                      />
                    </div>

                    {/* Preferred Date */}
                    <div>
                      <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Preferred Date (Optional)
                      </label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full justify-start text-left font-normal mt-1 rounded-lg border-gray-200 dark:border-gray-700 hover:border-primary dark:hover:border-primary-light",
                              !date && "text-muted-foreground",
                            )}
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {date ? format(date, "PPP") : <span>Pick a date</span>}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={date}
                            onSelect={setDate}
                            disabled={(date) => date < new Date() || date < new Date("1900-01-01")}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                    </div>

                    {/* Preferred Time */}
                    <div>
                      <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 block">
                        Preferred Time (Optional)
                      </label>
                      <RadioGroup value={timeSlot} onValueChange={setTimeSlot}>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                          {timeSlots.map((slot) => (
                            <div key={slot} className="flex items-center space-x-2">
                              <RadioGroupItem value={slot} id={slot} />
                              <Label htmlFor={slot} className="text-sm">
                                {slot}
                              </Label>
                            </div>
                          ))}
                        </div>
                      </RadioGroup>
                    </div>

                    {/* Services of Interest */}
                    <div>
                      <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 block">
                        Services You're Interested In (Optional)
                      </label>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {services.map((service) => (
                          <div key={service.id} className="flex items-center space-x-2">
                            <Checkbox
                              id={service.id}
                              checked={selectedServices.includes(service.id)}
                              onCheckedChange={(checked) => handleServiceChange(service.id, checked as boolean)}
                            />
                            <Label htmlFor={service.id} className="text-sm">
                              {service.name}
                            </Label>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Additional Information */}
                    <div>
                      <label htmlFor="additionalInfo" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Additional Information (Optional)
                      </label>
                      <Textarea
                        id="additionalInfo"
                        name="additionalInfo"
                        placeholder="Please share any specific needs, concerns, or questions you'd like us to address during the consultation."
                        className="mt-1 rounded-lg border-gray-200 dark:border-gray-700 focus:border-primary dark:focus:border-primary-light focus:ring-primary/10 dark:focus:ring-primary-light/10 transition-all duration-300 dark:bg-gray-800 dark:text-gray-100"
                      />
                    </div>

                    {/* SMS Consent Checkbox */}
                    <div className="space-y-3 p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700">
                      <div className="flex items-start space-x-3">
                        <Checkbox
                          id="smsConsent"
                          checked={smsConsent}
                          onCheckedChange={(checked) => setSmsConsent(checked as boolean)}
                          className="mt-1"
                        />
                        <label
                          htmlFor="smsConsent"
                          className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed"
                        >
                          By checking this box, I consent to receive text messages related to appointment reminders,
                          follow-ups, billing inquiries, and service updates from Journey of Care. You can reply
                          "STOP" at any time to opt out. Message and data rates may apply. Message frequency may vary,
                          text HELP to (************* for assistance. For more information, please refer to our{" "}
                          <Link
                            href="/privacy-policy"
                            className="text-[#1e3a8a] hover:text-[#1e40af] font-bold hover:underline"
                          >
                            privacy policy
                          </Link>
                          , and{" "}
                          <Link
                            href="/terms-conditions"
                            className="text-[#1e3a8a] hover:text-[#1e40af] font-bold hover:underline"
                          >
                            Terms and Conditions
                          </Link>{" "}
                          on our website.
                        </label>
                      </div>
                    </div>

                    <Button
                      type="submit"
                      disabled={isSubmitting}
                      className="w-full rounded-lg bg-primary hover:bg-primary-dark text-white py-6 h-auto transition-all duration-300 hover:shadow-lg"
                    >
                      {isSubmitting ? "Scheduling..." : "Schedule Free Consultation"}
                    </Button>

                    {submissionError && (
                      <p className="mt-4 text-red-600 dark:text-red-400 text-sm">{submissionError}</p>
                    )}
                  </form>
                )}
              </CardContent>
            </Card>

            {/* Contact Information */}
            <div className="mt-12 text-center bg-primary/10 dark:bg-primary/20 p-6 rounded-xl border border-primary/10 dark:border-primary-light/10">
              <h3 className="text-lg font-medium text-gray-800 dark:text-gray-100 mb-4">
                Prefer to Call? We're Here to Help!
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-4">
                You can also reach Journey of Care directly to schedule your consultation or ask any questions.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <a href="tel:+18324460705" className="text-primary hover:text-primary-dark font-medium text-lg">
                  (*************
                </a>
                <span className="hidden sm:inline text-gray-400">|</span>
                <a href="mailto:<EMAIL>" className="text-primary hover:text-primary-dark font-medium">
                  <EMAIL>
                </a>
              </div>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                Serving Conroe, TX and surrounding communities
              </p>
            </div>
          </motion.div>
        </div>
      </main>

      <Footer />
    </div>
  )
}
