import type { Metadata } from "next"
import AboutPageClient from "./about-client"

export const metadata: Metadata = {
  title: "About Journey of Care | Professional Home Health Care in Manvel, TX",
  description:
    "Learn about Journey of Care, led by <PERSON><PERSON>, RN. Providing compassionate home health care, personal care, and hospice services in Manvel, TX and surrounding areas.",
  keywords: [
    "about Journey of Care",
    "Derdine Lapaix-Fontil RN",
    "home health care Manvel TX",
    "healthcare mission",
    "professional home care team",
    "family-centered care",
  ],
  alternates: {
    canonical: "/about",
  },
  openGraph: {
    title: "About Journey of Care | Professional Home Health Care in Manvel, TX",
    description:
      "Learn about Journey of Care, led by <PERSON><PERSON>, RN. Providing compassionate home health care, personal care, and hospice services in Manvel, TX and surrounding areas.",
    url: "https://journey-of-care.com/about",
  },
}

export default function AboutPage() {
  return <AboutPageClient />
}
