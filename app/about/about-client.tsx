"use client"
import Image from "next/image"
import Link from "next/link"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, Heart, Users, Shield, Award, Phone, Mail, MapPin, Clock, Star, Quote } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import PageHero from "@/components/page-hero"
import { useInView } from "react-intersection-observer"

export default function AboutPageClient() {
  // Animation variants
  const fadeIn = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.6 } },
  }

  const fadeInLeft = {
    hidden: { opacity: 0, x: -20 },
    visible: { opacity: 1, x: 0, transition: { duration: 0.6 } },
  }

  const fadeInRight = {
    hidden: { opacity: 0, x: 20 },
    visible: { opacity: 1, x: 0, transition: { duration: 0.6 } },
  }

  const containerClass = "px-4 md:px-8 lg:px-16 xl:px-24 2xl:px-32 mx-auto max-w-[1600px]"

  // Intersection observer hooks
  const { ref: storyRef, inView: storyInView } = useInView({ triggerOnce: true, threshold: 0.1 })
  const { ref: valuesRef, inView: valuesInView } = useInView({ triggerOnce: true, threshold: 0.1 })
  const { ref: teamRef, inView: teamInView } = useInView({ triggerOnce: true, threshold: 0.1 })
  const { ref: missionRef, inView: missionInView } = useInView({ triggerOnce: true, threshold: 0.1 })

  // Core values data
  const coreValues = [
    {
      icon: Heart,
      title: "Compassion",
      description: "We approach every client with genuine care, empathy, and understanding of their unique situation.",
    },
    {
      icon: Shield,
      title: "Clinical Excellence",
      description: "We uphold the highest clinical standards in all our care and service delivery.",
    },
    {
      icon: Award,
      title: "Family-Centered Care",
      description: "We treat every client like family, ensuring personalized and respectful care.",
    },
    {
      icon: Users,
      title: "Professional Trust",
      description: "We honor the trust placed in us by providing reliable, professional healthcare services.",
    },
  ]

  // Team highlights
  const teamHighlights = [
    {
      title: "RN-Led Care",
      description: "All care is overseen by our registered nurse owner, ensuring clinical excellence.",
      icon: Shield,
    },
    {
      title: "Experienced Team",
      description: "Our caregivers are thoroughly trained and experienced in home healthcare.",
      icon: Award,
    },
    {
      title: "24/7 Support",
      description: "Round-the-clock availability for our hospice clients and emergency support.",
      icon: Clock,
    },
  ]

  // Testimonials
  const testimonials = [
    {
      quote:
        "My mom was hesitant about having someone come help her, but the caregiver from Journey of Care made her feel so comfortable. Now she actually looks forward to their visits! It's given our whole family peace of mind.",
      author: "Sarah M.",
      location: "The Woodlands, TX",
    },
    {
      quote:
        "After my dad's stroke, we weren't sure how to handle everything. Journey of Care stepped in and helped us navigate it all. Their team really knows what they're doing, and they treat him like family.",
      author: "David R.",
      location: "Spring, TX",
    },
  ]

  return (
    <div className="min-h-screen font-sans bg-white dark:bg-gray-950 overflow-hidden transition-colors duration-300">
      {/* Gradient Orbs */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
        <div className="absolute -top-40 -right-40 w-96 h-96 rounded-full bg-gradient-to-br from-primary/10 to-primary-light/5 dark:from-primary/5 dark:to-primary-light/2 blur-3xl"></div>
        <div className="absolute top-1/3 -left-40 w-80 h-80 rounded-full bg-gradient-to-tr from-secondary/10 to-secondary-light/5 dark:from-secondary/5 dark:to-secondary-light/2 blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-72 h-72 rounded-full bg-gradient-to-r from-[#757575]/10 to-[#9e9e9e]/5 dark:from-[#757575]/5 dark:to-[#9e9e9e]/2 blur-3xl"></div>
      </div>

      <Header />

      {/* Hero Section */}
      <PageHero
        title="About Journey of Care"
        subtitle="Compassionate healthcare services"
        imageSrc="https://res.cloudinary.com/dvauarkh6/image/upload/v1747925886/pexels-leish-6975092_lshkcl.jpg"
        imageAlt="Healthcare professional providing compassionate care"
      />

      {/* Main Content */}
      <main className="py-16 relative z-10">
        <div className={containerClass}>
          <div className="mb-8">
            <Link
              href="/"
              className="inline-flex items-center text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary-light transition-colors duration-200"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Home
            </Link>
          </div>

          {/* Our Story Section */}
          <motion.section
            ref={storyRef}
            initial="hidden"
            animate={storyInView ? "visible" : "hidden"}
            variants={fadeIn}
            className="mb-20"
          >
            <div className="text-center mb-12">
              <Badge className="px-4 py-1.5 bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary-light rounded-full text-sm font-medium mb-4">
                Our Story
              </Badge>
              <h1 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">
                Excellence in Home Healthcare
              </h1>
              <div className="w-20 h-1 bg-primary mx-auto rounded-full mb-6"></div>
            </div>

            <div className="grid md:grid-cols-2 gap-12 items-center">
              <motion.div variants={fadeInLeft} className="space-y-6">
                <h2 className="text-2xl font-medium text-gray-800 dark:text-gray-100">
                  Nurse-Owned & Operated
                </h2>
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                  Journey of Care is a nurse-owned home care agency serving North Houston and surrounding communities. Founded by a registered nurse, our team is built on compassion, professionalism, and a deep respect for the families we serve. We provide dependable in-home support tailored to each individual's needs — including care for older adults, individuals with special needs, and neurodivergent clients. Our services are rooted in dignity, trust, and a belief that everyone deserves to feel safe and valued in their own home.
                </p>
              </motion.div>

              <motion.div variants={fadeInRight} className="relative">
                <div className="w-full h-0 pb-[125%] relative rounded-2xl overflow-hidden shadow-xl">
                  <Image
                    src="in20.jpg"
                    alt="Owner of Journey of Care"
                    fill
                    className="object-cover rounded-2xl"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/30 to-transparent"></div>
                  <div className="absolute bottom-0 left-0 right-0 p-6">
                    <p className="text-white text-lg font-medium">Professional healthcare leadership</p>
                  </div>
                </div>
              </motion.div>
            </div>
          </motion.section>

          {/* Core Values Section */}
          <motion.section
            ref={valuesRef}
            initial="hidden"
            animate={valuesInView ? "visible" : "hidden"}
            variants={fadeIn}
            className="mb-20"
          >
            <div className="text-center mb-12">
              <Badge className="px-4 py-1.5 bg-secondary/10 dark:bg-secondary/20 text-secondary dark:text-secondary-light rounded-full text-sm font-medium mb-4">
                Our Values
              </Badge>
              <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">
                What Drives Our Care
              </h2>
              <div className="w-20 h-1 bg-secondary mx-auto rounded-full mb-6"></div>
              <p className="max-w-2xl mx-auto text-gray-600 dark:text-gray-300 text-lg">
                Our core values guide everything we do, ensuring that every client receives the highest quality care
                with dignity and respect.
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {coreValues.map((value, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={valuesInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                >
                  <Card className="h-full border border-gray-100 dark:border-gray-800 shadow-md hover:shadow-xl transition-all duration-300 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl">
                    <CardContent className="p-6 text-center">
                      <div className="w-16 h-16 rounded-full bg-primary/10 dark:bg-primary/20 flex items-center justify-center mx-auto mb-4">
                        <value.icon className="h-8 w-8 text-primary dark:text-primary-light" />
                      </div>
                      <h3 className="text-lg font-medium text-gray-800 dark:text-gray-100 mb-3">{value.title}</h3>
                      <p className="text-gray-600 dark:text-gray-300 text-sm">{value.description}</p>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.section>

          {/* Team Section */}
          <motion.section
            ref={teamRef}
            initial="hidden"
            animate={teamInView ? "visible" : "hidden"}
            variants={fadeIn}
            className="mb-20"
          >
            <div className="text-center mb-12">
              <Badge className="px-4 py-1.5 bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary-light rounded-full text-sm font-medium mb-4">
                Our Team
              </Badge>
              <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">
                Professional Excellence in Healthcare
              </h2>
              <div className="w-20 h-1 bg-primary mx-auto rounded-full mb-6"></div>
              <p className="max-w-2xl mx-auto text-gray-600 dark:text-gray-300 text-lg">
                Our team of dedicated healthcare professionals is committed to providing exceptional care with
                compassion and clinical expertise.
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              {teamHighlights.map((highlight, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={teamInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm p-6 rounded-xl shadow-md border border-gray-100 dark:border-gray-800"
                >
                  <div className="w-12 h-12 rounded-full bg-primary/10 dark:bg-primary/20 flex items-center justify-center mb-4">
                    <highlight.icon className="h-6 w-6 text-primary dark:text-primary-light" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-800 dark:text-gray-100 mb-2">{highlight.title}</h3>
                  <p className="text-gray-600 dark:text-gray-300 text-sm">{highlight.description}</p>
                </motion.div>
              ))}
            </div>
          </motion.section>

          {/* Mission Statement */}
          <motion.section
            ref={missionRef}
            initial="hidden"
            animate={missionInView ? "visible" : "hidden"}
            variants={fadeIn}
            className="mb-20"
          >
            <div className="bg-primary/5 dark:bg-primary/10 rounded-2xl p-8 md:p-12">
              <div className="max-w-4xl mx-auto text-center">
                <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">Our Mission</h2>
                <div className="w-20 h-1 bg-primary mx-auto rounded-full mb-8"></div>
                <p className="text-xl text-gray-700 dark:text-gray-300 leading-relaxed mb-8">
                  "At Journey of Care, we believe everyone deserves to be treated like family — with patience, kindness, and real attention. Our mission is to bring comfort, connection, and peace of mind into the everyday lives of the people we serve. Whether it's preparing a meal, offering a listening ear, or helping with daily tasks, we show up with intention — because care isn't just a service, it's a relationship. At Journey of Care, every journey begins with care — and continues with trust."
                </p>
                <div className="grid md:grid-cols-3 gap-6 mt-8">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-primary mb-2">24/7</div>
                    <p className="text-gray-600 dark:text-gray-300">On-Call Support</p>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-primary mb-2">RN</div>
                    <p className="text-gray-600 dark:text-gray-300">Led Care Team</p>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-primary mb-2">100%</div>
                    <p className="text-gray-600 dark:text-gray-300">Family-Centered</p>
                  </div>
                </div>
              </div>
            </div>
          </motion.section>

          {/* Testimonials */}
          <motion.section initial="hidden" animate="visible" variants={fadeIn} className="mb-20">
            <div className="text-center mb-12">
              <Badge className="px-4 py-1.5 bg-secondary/10 dark:bg-secondary/20 text-secondary dark:text-secondary-light rounded-full text-sm font-medium mb-4">
                Client Stories
              </Badge>
              <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">
                What Our Families Say
              </h2>
              <div className="w-20 h-1 bg-secondary mx-auto rounded-full mb-6"></div>
            </div>

            <div className="grid md:grid-cols-2 gap-8">
              {testimonials.map((testimonial, index) => (
                <Card
                  key={index}
                  className="border border-gray-100 dark:border-gray-800 shadow-md bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl"
                >
                  <CardContent className="p-6">
                    <div className="flex items-center mb-4">
                      <Quote className="h-5 w-5 text-primary mr-2" />
                      <div className="flex">
                        {[...Array(5)].map((_, i) => (
                          <Star key={i} className="h-4 w-4 text-yellow-500 fill-current" />
                        ))}
                      </div>
                    </div>
                    <p className="text-gray-600 dark:text-gray-300 mb-4 italic">"{testimonial.quote}"</p>
                    <div>
                      <p className="text-sm font-medium text-primary">{testimonial.author}</p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">{testimonial.location}</p>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </motion.section>

          {/* Contact CTA */}
          <motion.section
            initial="hidden"
            animate="visible"
            variants={fadeIn}
            className="bg-gradient-to-r from-primary/10 to-secondary/10 dark:from-primary/20 dark:to-secondary/20 rounded-2xl p-8 md:p-12 text-center"
          >
            <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-4">
              Ready to Experience Our Care?
            </h2>
            <p className="text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
              Contact Journey of Care today to learn more about our services and how we can help your family.
            </p>

            <div className="grid md:grid-cols-3 gap-6 mb-8">
              <div className="flex items-center justify-center gap-2">
                <Phone className="h-5 w-5 text-primary" />
                <a href="tel:+18324460705" className="text-primary hover:underline font-medium">
                  (*************
                </a>
              </div>
              <div className="flex items-center justify-center gap-2">
                <Mail className="h-5 w-5 text-primary" />
                <a href="mailto:<EMAIL>" className="text-primary hover:underline font-medium">
                  <EMAIL>
                </a>
              </div>
              <div className="flex items-center justify-center gap-2">
                <MapPin className="h-5 w-5 text-primary" />
                <span className="text-gray-600 dark:text-gray-300">Conroe, TX</span>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/consultation">
                <Button className="rounded-full bg-primary hover:bg-primary-dark text-white px-8 py-3 transition-all duration-300 hover:shadow-lg">
                  Schedule Free Assessment
                </Button>
              </Link>
              <Link href="/contact">
                <Button
                  variant="outline"
                  className="rounded-full border-primary text-primary hover:bg-primary hover:text-white px-8 py-3 transition-all duration-300"
                >
                  Contact Us
                </Button>
              </Link>
            </div>
          </motion.section>
        </div>
      </main>

      <Footer />
    </div>
  )
}
