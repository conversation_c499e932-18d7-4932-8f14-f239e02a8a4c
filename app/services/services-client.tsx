"use client"
import { cn } from "@/lib/utils"
import { useState, useEffect } from "react"
import { useSearchParams, useRouter } from "next/navigation"
import Image from "next/image"
import Link from "next/link"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  ArrowLeft,
  Heart,
  Users,
  Clock,
  Building2,
  Phone,
  Shield,
  Mail,
  MapPin,
  CheckCircle,
  Star,
} from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import PageHero from "@/components/page-hero"

export default function ServicesPageClient() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("inclusive-care")

  // Update active tab based on URL parameter
  useEffect(() => {
    const serviceParam = searchParams.get("service")
    if (serviceParam) {
      setActiveTab(serviceParam)
    }
  }, [searchParams])

  // Handle tab change and update URL
  const handleTabChange = (value: string) => {
    setActiveTab(value)
    const newUrl = `/services?service=${value}`
    router.push(newUrl, { scroll: false })
  }

  const containerClass = "px-4 md:px-8 lg:px-16 xl:px-24 2xl:px-32 mx-auto max-w-[1600px]"

  const services = [
    {
      id: "inclusive-care",
      title: "Inclusive Care for All Abilities",
      subtitle: "Specialized Support for Neurodivergent and Special Needs Clients",
      icon: Heart,
      image:
        "/images/pexels-cliff-booth-4058220.jpg",
      description:
        "We proudly support individuals with autism, intellectual disabilities, and neurodivergent needs. Our caregivers are trained in communication strategies and sensory-friendly care to build trust and create safe, empowering environments for every client.",
      features: [
        "Autism and neurodivergent support",
        "Intellectual disability care",
        "Communication strategies and sensory-friendly approaches",
        "Trust-building and empowerment techniques",
        "Safe, understanding environments",
        "Individualized care plans",
      ],
      benefits: [
        "Specialized training for unique needs",
        "Empowering and safe environments",
        "Trust-building relationships",
        "Family peace of mind",
      ],
      boxed: {
  title: "Ready to Embrace Truly Inclusive Care?",
  subtitle: "Let’s talk about how we can support neurodivergent and special needs individuals with care that’s personalized, empowering, and safe.",
  primary_cta: "Schedule a Free Consultation",
  secondary_cta: "Speak With a Specialist"
}

    },
    {
      id: "personal-care",
      title: "Personal Care Services",
      subtitle: "Respectful Support for Every Stage of Life",
      icon: Heart,
      image:
        "https://res.cloudinary.com/dvauarkh6/image/upload/v1752939765/pexels-kampus-7551653_ala4wc.jpg",
      description:
        "At Journey of Care, we provide personal care with heart. Whether you're aging in place, living with a disability, or seeking support for a neurodivergent loved one, we're here to help with the daily routines that matter most — with warmth, dignity, and respect. Our care is never one-size-fits-all. We build real relationships, offering support that's tailored to each person's comfort, preferences, and unique story. You're not just receiving help — you're gaining a trusted partner in care.",
      features: [
        "Support with bathing, grooming, and dressing",
        "Safe assistance with walking, transferring, and mobility",
        "Toileting and incontinence care",
        "Medication reminders only (non-medical support)",
        "Personal hygiene and routine assistance",
        "Meal prep and eating support",
        "Friendly companionship and presence",
      ],
      benefits: [
        "Encourages independence, confidence, and connection",
        "Eases the emotional and physical load on families",
        "Flexible support built around your daily rhythm",
        "A team that truly cares — and shows it every visit",
      ],
      boxed:{
        title:"Ready for Personalized Care That Feels Like Home?",
        subtitle:"Let’s start with a conversation. Schedule your free care consultation to find the support that’s just right for you or your loved one.",
        primary_cta:"Schedule My Free Assessment",
        secondary_cta: "Contact Our Team"
      }
    },
    {
      id: "companion-care",
      title: "Companion Care",
      subtitle: "Genuine Connections. Meaningful Moments.",
      icon: Users,
      image: "https://res.cloudinary.com/dvauarkh6/image/upload/v1747925886/pexels-leish-6975092_lshkcl.jpg",
      description:
        "At Journey of Care, we know that emotional well-being is just as important as physical care. Our Companion Care services are designed to bring joy, engagement, and connection to everyday life — whether it's through conversation, hobbies, or simply having someone there who truly listens. Whether you're supporting an aging parent, a neurodivergent adult, or someone recovering from illness, we tailor every visit to their unique needs, preferences, and pace. It's not just about passing time — it's about sharing it.",
      features: [
        "Heartfelt conversation and real companionship",
        "Light housekeeping and simple meal prep",
        "Help with errands and appointments",
        "Shared hobbies and creative activities",
        "Safe neighborhood walks and time outdoors",
        "Help with video calls, texts, and staying connected",
      ],
      benefits: [
        "Less loneliness, more connection",
        "Encourages emotional and mental wellness",
        "Supports cognitive health through engagement",
        "Brings joy, structure, and purpose to each day",
        "Builds trust and peace of mind for families",
      ],
      boxed:{
        title:"Want to Bring Warmth & Connection Into Your Routine?",
        subtitle:"Let’s chat about how we can bring meaningful support to someone you love.",
        primary_cta:"Schedule Free Assessment",
        secondary_cta: "Reach Out Today"
      }
    },
    {
      id: "respite-care",
      title: "Respite Care",
      subtitle: "Because Caregivers Deserve Care Too",
      icon: Clock,
      image: "https://res.cloudinary.com/dvauarkh6/image/upload/v1752939728/pexels-jsme-mila-523821574-18459209_ehgryf.jpg",
      description:
        "At Journey of Care, we understand that caregiving is full of love — but it can also be exhausting. Our respite care services are here to provide trusted relief, whether you need a few hours, a few days, or something more regular. Whether you're caring for an elderly parent, a partner with chronic needs, or a neurodivergent loved one, we step in with compassion and consistency, so you can take a breath, knowing they're in good hands.",
      features: [
        "Flexible scheduling built around your routine",
        "Compassionate, experienced non-medical caregivers",
        "Short-term and long-term relief options",
        "Emergency respite care when you need it most",
        "Clear communication and real-time updates on care",
      ],
      benefits: [
        "Restores balance and avoids burnout",
        "Frees up time for your own self-care",
        "Peace of mind knowing your loved one is safe",
        "Keeps care routines consistent and reliable",
      ]
      ,
      boxed:{
        title:"Need a Break Without the Guilt?",
        subtitle:"Let us support you while you support the ones you love.",
        primary_cta:"Schedule a Free Assessment",
        secondary_cta: "Talk With Our Team"
      }
    },
    {
      id: "in-facility-care",
      title: "In-Facility Care",
      subtitle: "Personal Support That Goes Beyond the Call Light",
      icon: Building2,
      image: "https://res.cloudinary.com/dvauarkh6/image/upload/v1744967360/pexels-kampus-7551684_z6lvh6.jpg",
      description:
        "Even in assisted living or senior communities, some residents need a little more — more time, more attention, more heart. That's where we come in. Our in-facility care services provide dedicated one-on-one support, working alongside staff to make sure your loved one feels seen, safe, and genuinely cared for.",
      features: [
        "Friendly companionship and emotional connection",
        "Help with grooming, meals, and daily personal tasks",
        "Extra eyes and ears to support safety and comfort",
        "Advocacy and coordination with facility staff",
        "Ongoing communication with family",
      ],
      benefits: [
        "A higher level of personalized attention",
        "Better quality of life within a shared setting",
        "Stronger collaboration between caregivers and staff",
        "Peace of mind knowing someone is fully focused on your loved one",
      ]
      ,
      boxed:{
        title:"Want Someone Who’s Fully Focused on Them?",
        subtitle:"We provide the added layer of care you’d give yourself if you could.",
        primary_cta:"Schedule a Free Assessment",
        secondary_cta: "Contact Our Team"
      }
    },
  ]

  const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.6 } },
  }

  return (
    <div className="min-h-screen font-sans bg-white dark:bg-gray-950 overflow-hidden transition-colors duration-300">
      {/* Gradient Orbs */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
        <div className="absolute -top-40 -right-40 w-96 h-96 rounded-full bg-gradient-to-br from-primary/10 to-primary-light/5 dark:from-primary/5 dark:to-primary-light/2 blur-3xl"></div>
        <div className="absolute top-1/3 -left-40 w-80 h-80 rounded-full bg-gradient-to-tr from-secondary/10 to-secondary-light/5 dark:from-secondary/5 dark:to-secondary-light/2 blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-72 h-72 rounded-full bg-gradient-to-r from-[#757575]/10 to-[#9e9e9e]/5 dark:from-[#757575]/5 dark:to-[#9e9e9e]/2 blur-3xl"></div>
      </div>

      <Header />

      {/* Hero Section */}
      <PageHero
        title="Our Services"
        subtitle="Comprehensive home care services tailored to your needs in North Houston, Conroe, The Woodlands, Spring, and
surrounding areas."
        imageSrc="https://res.cloudinary.com/dvauarkh6/image/upload/v1753007295/pexels-shkrabaanthony-7345463_pebvew.jpg"
        imageAlt="Caregiver providing compassionate care"
      />

      {/* Main Content */}
      <main className="py-16 relative z-10">
        <div className={containerClass}>
          <div className="mb-8">
            <Link
              href="/"
              className="inline-flex items-center text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary-light transition-colors duration-200"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Home
            </Link>
          </div>

          <motion.div initial="hidden" animate="visible" variants={fadeIn} className="max-w-6xl mx-auto">
            <div className="text-center mb-12">
              <Badge className="px-4 py-1.5 bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary-light rounded-full text-sm font-medium mb-4">
                Services We Provide
              </Badge>
              <h1 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">
                Comprehensive Home Care Services
              </h1>
              <p className="text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                At Journey of Care, we provide a comprehensive range of home care services tailored to support the
                unique needs of seniors and individuals requiring daily assistance. Explore our specialized care options
                below.
              </p>
            </div>

            {/* Services Tabs */}
            <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
              <TabsList className="grid w-full grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 mb-8 h-auto min-h-[60px] md:min-h-[40px]">
                {services.map((service) => (
                  <TabsTrigger
                    key={service.id}
                    value={service.id}
                    className="text-xs md:text-sm px-1 md:px-2 py-2 data-[state=active]:bg-primary data-[state=active]:text-white flex flex-col md:flex-row items-center gap-1"
                  >
                    <service.icon className="h-3 w-3 md:h-4 md:w-4 md:mr-1" />
                    <span className="text-center leading-tight">{service.title}</span>
                  </TabsTrigger>
                ))}
              </TabsList>

              {services.map((service) => (
                <TabsContent key={service.id} value={service.id} className="mt-8 pt-4 md:pt-0">
                  <Card className="border border-gray-100 dark:border-gray-800 shadow-lg bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl overflow-hidden">
                    <CardContent className="p-0">
                      <div className="grid lg:grid-cols-2 gap-0">
                        {/* Service Image */}
                        <div className="relative h-64 lg:h-full">
                          <Image
                            src={service.image || "/placeholder.svg"}
                            alt={`${service.title} - Journey of Care`}
                            fill
                            className="object-cover"
                            sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 40vw"
                          />
                          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent"></div>
                          <div className="absolute bottom-4 left-4 right-4">
                            <div className="flex items-center mb-2">
                              <div className="w-12 h-12 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center mr-3">
                                <service.icon className="h-6 w-6 text-white" />
                              </div>
                              <div>
                                <h3 className="text-white font-medium text-lg">{service.title}</h3>
                                <p className="text-white/90 text-sm">{service.subtitle}</p>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Service Info */}
                        <div className="p-8 lg:p-12">
                          <div className="flex items-center mb-6">
                            <div className="w-16 h-16 rounded-full bg-primary/10 dark:bg-primary/20 flex items-center justify-center mr-4">
                              <service.icon className="h-8 w-8 text-primary dark:text-primary-light" />
                            </div>
                            <div>
                              <h2 className="text-2xl font-medium text-gray-800 dark:text-gray-100">{service.title}</h2>
                              <p className="text-primary dark:text-primary-light font-medium">{service.subtitle}</p>
                            </div>
                          </div>

                          <p className="text-gray-600 dark:text-gray-300 mb-8 leading-relaxed">{service.description}</p>

                          <div className="space-y-6">
                            <div>
                              <h3 className="text-lg font-medium text-gray-800 dark:text-gray-100 mb-4">
                                What We Provide:
                              </h3>
                              <ul className="space-y-2">
                                {service.features.map((feature, index) => (
                                  <li key={index} className="flex items-start">
                                    <CheckCircle className="h-5 w-5 text-primary dark:text-primary-light mr-3 mt-0.5 flex-shrink-0" />
                                    <span className="text-gray-600 dark:text-gray-300">{feature}</span>
                                  </li>
                                ))}
                              </ul>
                            </div>

                            <div>
                              <h3 className="text-lg font-medium text-gray-800 dark:text-gray-100 mb-4">
                                Key Benefits:
                              </h3>
                              <ul className="space-y-2">
                                {service.benefits.map((benefit, index) => (
                                  <li key={index} className="flex items-start">
                                    <Star className="h-5 w-5 text-secondary dark:text-secondary-light mr-3 mt-0.5 flex-shrink-0" />
                                    <span className="text-gray-600 dark:text-gray-300">{benefit}</span>
                                  </li>
                                ))}
                              </ul>
                            </div>

                            <div className="bg-primary/5 dark:bg-primary/10 p-6 rounded-lg border border-primary/10 dark:border-primary/20">
                              <h4 className="text-lg font-medium text-gray-800 dark:text-gray-100 mb-4">
                                {
                                  service.boxed?.title
                                }
                              </h4>
                              <p className="text-gray-600 dark:text-gray-300 mb-6">
                                {
                                  service.boxed?.subtitle
                                }
                              </p>
                              <div className="flex flex-col sm:flex-row gap-3">
                                <Link href="/consultation" className="flex-1">
                                  <Button className="w-full bg-primary hover:bg-primary-dark text-white">
                                   {service.boxed?.primary_cta}
                                  </Button>
                                </Link>
                                <Link href="/contact" className="flex-1">
                                  <Button
                                    variant="outline"
                                    className="w-full border-gray-200 dark:border-gray-700 bg-transparent"
                                  >
                                    {service.boxed?.secondary_cta}
                                  </Button>
                                </Link>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              ))}
            </Tabs>


               <section className="py-16 relative z-10 bg-gradient-to-b from-[#f9f9f9] to-white dark:from-gray-900 dark:to-gray-950 border-b border-gray-100 dark:border-gray-800">
                        <div className={cn(containerClass)}>
                          <div className="max-w-4xl mx-auto text-center">
                            <div className="inline-block">
                              <Badge className="px-4 py-1.5 bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary-light rounded-full text-sm font-medium mb-4">
                                Payment Information
                              </Badge>
                            </div>
                            <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">
                              Private Pay Only
                            </h2>
                            <div className="w-20 h-1 bg-primary mx-auto rounded-full mb-8"></div>
                            <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed">
                              Journey of Care accepts all major credit and debit cards through secure online billing. We do not accept checks, cash, or insurance plans.
                            </p>
            
                            {/* Payment Methods Icons */}
                            <div className="mt-8 flex justify-center items-center gap-6 flex-wrap">
                              <div className="flex items-center gap-2 text-gray-600 dark:text-gray-300">
                                <div className="w-8 h-8 bg-primary/10 dark:bg-primary/20 rounded-lg flex items-center justify-center">
                                  <svg className="w-5 h-5 text-primary" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M2 4h20v16H2V4zm2 2v12h16V6H4zm2 2h12v2H6V8zm0 4h8v2H6v-2z"/>
                                  </svg>
                                </div>
                                <span className="text-sm font-medium">Credit Cards</span>
                              </div>
                              <div className="flex items-center gap-2 text-gray-600 dark:text-gray-300">
                                <div className="w-8 h-8 bg-primary/10 dark:bg-primary/20 rounded-lg flex items-center justify-center">
                                  <svg className="w-5 h-5 text-primary" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M2 4h20v16H2V4zm2 2v12h16V6H4zm2 2h12v2H6V8zm0 4h8v2H6v-2z"/>
                                  </svg>
                                </div>
                                <span className="text-sm font-medium">Debit Cards</span>
                              </div>
                              <div className="flex items-center gap-2 text-gray-600 dark:text-gray-300">
                                <div className="w-8 h-8 bg-primary/10 dark:bg-primary/20 rounded-lg flex items-center justify-center">
                                  <Shield className="w-5 h-5 text-primary" />
                                </div>
                                <span className="text-sm font-medium">Secure Online Billing</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </section>
            {/* Contact CTA Section */}
            <div className="mt-16 bg-primary/10 dark:bg-primary/20 p-8 rounded-xl border border-primary/10 dark:border-primary-light/10">
              <div className="text-center mb-8">
                <h2 className="text-2xl font-light text-gray-800 dark:text-gray-100 mb-4">
                  Ready to Learn More About Our Services?
                </h2>
                <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                  Contact Journey of Care today to schedule your free in-home care consultation. Let us create a
                  personalized care plan for your loved one!
                </p>
              </div>

              <div className="grid md:grid-cols-3 gap-6 mb-8">
                <div className="text-center">
                  <div className="w-12 h-12 rounded-full bg-white dark:bg-gray-800 flex items-center justify-center mx-auto mb-3">
                    <Phone className="h-6 w-6 text-primary dark:text-primary-light" />
                  </div>
                  <h3 className="font-medium text-gray-800 dark:text-gray-100 mb-1">Call Us</h3>
                  <a href="tel:+18324460705" className="text-primary hover:underline">
                    (*************
                  </a>
                </div>

                <div className="text-center">
                  <div className="w-12 h-12 rounded-full bg-white dark:bg-gray-800 flex items-center justify-center mx-auto mb-3">
                    <Mail className="h-6 w-6 text-primary dark:text-primary-light" />
                  </div>
                  <h3 className="font-medium text-gray-800 dark:text-gray-100 mb-1">Email Us</h3>
                  <a href="mailto:<EMAIL>" className="text-primary hover:underline">
                    <EMAIL>
                  </a>
                </div>

                <div className="text-center">
                  <div className="w-12 h-12 rounded-full bg-white dark:bg-gray-800 flex items-center justify-center mx-auto mb-3">
                    <MapPin className="h-6 w-6 text-primary dark:text-primary-light" />
                  </div>
                  <h3 className="font-medium text-gray-800 dark:text-gray-100 mb-1">Service Area</h3>
                  <p className="text-gray-600 dark:text-gray-300">Conroe, TX & Surrounding Communities</p>
                </div>
              </div>

              <div className="text-center">
                <Link href="/consultation">
                  <Button className="bg-primary hover:bg-primary-dark text-white px-8 py-3">
                    Schedule Your Free Assessment Today
                  </Button>
                </Link>
              </div>
            </div>

            {/* FAQ Section */}
            <div className="mt-16">
              <h2 className="text-2xl font-light text-gray-800 dark:text-gray-100 mb-8 text-center">
                Frequently Asked Questions
              </h2>
              <div className="grid md:grid-cols-2 gap-6">
                <Card className="border border-gray-100 dark:border-gray-800 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
                  <CardContent className="p-6">
                    <h3 className="font-medium text-gray-800 dark:text-gray-100 mb-3">What areas do you serve?</h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      We proudly serve Conroe, TX and surrounding communities, providing comprehensive home care
                      services throughout the area.
                    </p>
                  </CardContent>
                </Card>

                <Card className="border border-gray-100 dark:border-gray-800 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
                  <CardContent className="p-6">
                    <h3 className="font-medium text-gray-800 dark:text-gray-100 mb-3">
                      Are your caregivers licensed and insured?
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      Yes, all our caregivers are thoroughly screened, trained, certified, and fully insured to provide
                      safe, professional care.
                    </p>
                  </CardContent>
                </Card>

                <Card className="border border-gray-100 dark:border-gray-800 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
                  <CardContent className="p-6">
                    <h3 className="font-medium text-gray-800 dark:text-gray-100 mb-3">How do you create care plans?</h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      We conduct a comprehensive assessment of your loved one's needs and preferences to create a
                      customized care plan that evolves with their changing requirements.
                    </p>
                  </CardContent>
                </Card>

                <Card className="border border-gray-100 dark:border-gray-800 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
                  <CardContent className="p-6">
                    <h3 className="font-medium text-gray-800 dark:text-gray-100 mb-3">Do you provide 24/7 care?</h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      Yes, we offer flexible scheduling including 24/7 care options and emergency support to meet your
                      family's needs.
                    </p>
                  </CardContent>
                </Card>
              </div>
            </div>
          </motion.div>
        </div>
      </main>

      <Footer />
    </div>
  )
}
