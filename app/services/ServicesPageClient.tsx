"use client"
import { useState, useEffect } from "react"
import { useSearchPara<PERSON>, useRouter } from "next/navigation"
import Image from "next/image"
import Link from "next/link"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from "@/components/ui/sheet"
import { ArrowLeft, HeartHandshake, Menu, Home, Brain, Sparkles, Shield, Users, Stethoscope } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import PageHero from "@/components/page-hero"

// Services data
const services = [
  {
    id: "personal-care",
    title: "Personal Care Services",
    subtitle: "Assistance with Daily Living Activities",
    description:
      "Our Personal Care Services provide dignified assistance with daily living activities like bathing, grooming, and dressing to help maintain independence and personal hygiene.",
    icon: Home,
    color: "#274166",
    bgColor: "#e6f2ff",
    image: "https://res.cloudinary.com/dvauarkh6/image/upload/v1752939765/pexels-kampus-7551653_ala4wc.jpg",
    benefits: [
      "Bathing, dressing, and grooming assistance",
      "Personal hygiene and toileting support",
      "Mobility and transfer assistance",
      "Medication reminders and management",
    ],
    idealFor: [
      "Seniors who need help with daily activities",
      "Individuals with disabilities requiring personal care support",
      "Those recovering from surgery or illness",
      "Anyone needing short-term or ongoing personal care",
    ],
    keyBenefits: [
      "Maintain dignity and independence at home",
      "Ensure proper hygiene and personal care",
      "Reduce risk of falls and injuries",
      "Provide peace of mind for family members",
    ],
    addOns: ["Companion care services", "Light housekeeping", "Meal preparation", "Transportation to appointments"],
  },
  {
    id: "companion-care",
    title: "Companion Care",
    subtitle: "Emotional Support and Daily Assistance",
    description:
      "Our Companion Care services provide emotional support, conversation, and help with errands to combat loneliness and enhance quality of life for seniors and individuals who need social interaction.",
    icon: Users,
    color: "#f8d134",
    bgColor: "#fff9e6",
    image: "https://res.cloudinary.com/dvauarkh6/image/upload/v1743511643/pexels-mikhail-nilov-6981096_abbqxk.jpg",
    benefits: [
      "Friendly conversations and social engagement",
      "Assistance with errands and appointments",
      "Light housekeeping and meal preparation",
      "Recreational activities and companionship",
    ],
    idealFor: [
      "Seniors living alone who need social interaction",
      "Individuals who want to remain active and engaged",
      "Those who need assistance with daily tasks",
      "Family members seeking peace of mind",
    ],
    keyBenefits: [
      "Reduce feelings of isolation and loneliness",
      "Maintain social connections and mental stimulation",
      "Assistance with daily tasks and activities",
      "Enhanced quality of life and well-being",
    ],
    addOns: [
      "Transportation to social events",
      "Technology assistance for family communication",
      "Hobby and interest support",
      "Light personal care services",
    ],
  },
  {
    id: "respite-care",
    title: "Respite Care",
    subtitle: "Temporary Relief for Family Caregivers",
    description:
      "Our Respite Care services provide temporary relief for family caregivers to rest and recharge while ensuring your loved one receives quality, professional care in their absence.",
    icon: HeartHandshake,
    color: "#6b7280",
    bgColor: "#f3f4f6",
    image: "https://res.cloudinary.com/dvauarkh6/image/upload/v1743512842/pexels-kampus-7551617_txoyqk.jpg",
    benefits: [
      "Flexible scheduling to meet your needs",
      "Experienced caregivers for temporary care",
      "Comprehensive care matching regular routines",
      "Peace of mind for family caregivers",
    ],
    idealFor: [
      "Family caregivers who need a break",
      "Families planning vacations or special events",
      "Primary caregivers experiencing burnout",
      "Situations requiring temporary additional support",
    ],
    keyBenefits: [
      "Prevent caregiver burnout and stress",
      "Maintain your own health and well-being",
      "Ensure continuity of care for your loved one",
      "Flexible scheduling when you need it most",
    ],
    addOns: [
      "Overnight care options",
      "Extended multi-day care",
      "Special event preparation",
      "Emergency respite services",
    ],
  },
  {
    id: "specialized-services",
    title: "Specialized Services",
    subtitle: "Expert Care for Chronic and Cognitive Conditions",
    description:
      "Our Specialized Services provide expert support for clients with Alzheimer's, dementia, or other chronic conditions, delivered by trained and compassionate caregivers.",
    icon: Brain,
    color: "#274166",
    bgColor: "#e6f2ff",
    image: "https://res.cloudinary.com/dvauarkh6/image/upload/v1743511647/pexels-shvets-production-8899548_s2jziq.jpg",
    benefits: [
      "Alzheimer's and dementia care support",
      "Chronic condition management assistance",
      "Specialized training for complex needs",
      "Customized care plans for specific conditions",
    ],
    idealFor: [
      "Individuals with Alzheimer's or dementia",
      "Those with chronic medical conditions",
      "People requiring specialized care approaches",
      "Families seeking expert condition-specific support",
    ],
    keyBenefits: [
      "Specialized training for specific conditions",
      "Enhanced safety and reduced anxiety",
      "Maintenance of dignity and quality of life",
      "Support for both individual and family",
    ],
    addOns: [
      "Memory enhancement activities",
      "Specialized mobility assistance",
      "Healthcare provider coordination",
      "Family caregiver education",
    ],
  },
  {
    id: "in-facility-care",
    title: "In-Facility Care",
    subtitle: "Personal Care in Healthcare Facilities",
    description:
      "Our In-Facility Care services provide personal care in assisted living or healthcare facilities, supplementing existing services with one-on-one attention and advocacy.",
    icon: Shield,
    color: "#f8d134",
    bgColor: "#fff9e6",
    image: "https://res.cloudinary.com/dvauarkh6/image/upload/v1747917955/pexels-yaroslav-shuraev-8088910_phrxs8.jpg",
    benefits: [
      "One-on-one companionship and advocacy",
      "Personal care assistance in facilities",
      "Monitoring for comfort and safety",
      "Coordination with facility staff",
    ],
    idealFor: [
      "Residents of assisted living facilities",
      "Individuals in healthcare facilities needing extra support",
      "Families wanting personalized attention for loved ones",
      "Those transitioning to facility living",
    ],
    keyBenefits: [
      "Enhanced quality of life in facility settings",
      "Personalized attention beyond standard care",
      "Advocacy for individual needs and preferences",
      "Peace of mind for family members",
    ],
    addOns: [
      "Accompaniment to facility activities",
      "Personal shopping and errands",
      "Technology assistance for family communication",
      "Specialized memory care support",
    ],
  },
  {
    id: "pdn-services",
    title: "Private Duty Nursing (PDN)",
    subtitle: "Skilled One-on-One Nursing Care at Home",
    description:
      "Our Private Duty Nursing (PDN) services provide skilled, compassionate one-on-one nursing care tailored to individual medical needs. Our registered nurses deliver specialized care for complex medical conditions in the comfort of your home.",
    icon: Stethoscope,
    color: "#e05691",
    bgColor: "#fdf2f8",
    image: "https://res.cloudinary.com/dvauarkh6/image/upload/v1749898932/20250614_1200_Child_on_Ventilator_simple_compose_01jxq1f9q3e36r5je8a267q7x8_h7urwi.png",
    benefits: [
      "24/7 skilled nursing care availability",
      "Complex medical condition management",
      "Ventilator and tracheostomy care",
      "IV therapy and medication administration",
      "Wound care and post-surgical support",
      "Chronic disease monitoring",
      "Family education and support",
      "Coordination with healthcare team",
    ],
    idealFor: [
      "Patients with complex medical conditions",
      "Individuals requiring ventilator support",
      "Those needing IV therapy or specialized medications",
      "Post-surgical patients with complex care needs",
      "Clients with tracheostomies or feeding tubes",
      "Anyone requiring skilled nursing supervision",
    ],
    keyBenefits: [
      "Personalized one-on-one nursing attention",
      "Avoid lengthy hospital stays",
      "Maintain dignity and comfort at home",
      "Expert management of complex conditions",
      "Family involvement in care decisions",
      "Cost-effective alternative to facility care",
    ],
    addOns: [
      "24/7 nursing coverage options",
      "Specialized equipment management",
      "Family caregiver training",
      "Emergency response protocols",
    ],
  },
]

export default function ServicesPageClientComponent() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const [activeService, setActiveService] = useState<string | null>(null)
  const [sideNavOpen, setSideNavOpen] = useState(false)

  useEffect(() => {
    // Get the service from URL query parameter
    const serviceParam = searchParams.get("service")
    if (serviceParam) {
      setActiveService(serviceParam)
    } else if (services.length > 0) {
      // Default to first service if none specified
      setActiveService(services[0].id)
    }
  }, [searchParams])

  // Handle service selection from side nav
  const handleServiceSelect = (serviceId: string) => {
    setActiveService(serviceId)
    setSideNavOpen(false)

    // Update the URL without full page reload
    router.push(`/services?service=${serviceId}`, { scroll: false })
  }

  // Find the currently active service
  const currentService = services.find((service) => service.id === activeService) || services[0]

  // Animation variants
  const fadeIn = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.6 } },
  }

  const containerClass = "px-4 md:px-8 lg:px-16 xl:px-24 2xl:px-32 mx-auto max-w-[1600px]"

  return (
    <div className="min-h-screen font-sans bg-white dark:bg-gray-950 overflow-x-hidden transition-colors duration-300">
      {/* Gradient Orbs */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
        <div className="absolute -top-40 -right-40 w-96 h-96 rounded-full bg-gradient-to-br from-[#6B7F3F]/10 to-[#8DA350]/5 dark:from-[#6B7F3F]/5 dark:to-[#8DA350]/2 blur-3xl"></div>
        <div className="absolute top-1/3 -left-40 w-80 h-80 rounded-full bg-gradient-to-tr from-[#C26E3E]/10 to-[#E08D5C]/5 dark:from-[#C26E3E]/5 dark:to-[#E08D5C]/2 blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-72 h-72 rounded-full bg-gradient-to-r from-[#757575]/10 to-[#9e9e9e]/5 dark:from-[#757575]/5 dark:to-[#9e9e9e]/2 blur-3xl"></div>
      </div>

      <Header />

      {/* Hero Section */}
      <PageHero
        title="Our Services"
        subtitle="Comprehensive care solutions tailored to your needs"
        imageSrc="https://res.cloudinary.com/dvauarkh6/image/upload/v1747917952/pexels-vlada-karpovich-5790702_1_jsorsb.jpg"
        imageAlt="Healthcare professional providing services"
      />

      {/* Main Content */}
      <main className="py-16 relative z-10">
        <div className={containerClass}>
          <div className="mb-8">
            <Link
              href="/"
              className="inline-flex items-center text-gray-600 dark:text-gray-300 hover:text-[#6B7F3F] dark:hover:text-[#8DA350] transition-colors duration-200"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Home
            </Link>
          </div>

          <motion.div initial="hidden" animate="visible" variants={fadeIn}>
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100">Available Services</h2>

              {/* Mobile Services Menu Button */}
              <Sheet open={sideNavOpen} onOpenChange={setSideNavOpen}>
                <SheetTrigger asChild>
                  <Button variant="outline" size="icon" className="md:hidden border-gray-200 dark:border-gray-700">
                    <Menu className="h-4 w-4" />
                    <span className="sr-only">Open services menu</span>
                  </Button>
                </SheetTrigger>
                <SheetContent side="left" className="w-[280px] sm:w-[350px] p-0">
                  <div className="h-full flex flex-col">
                    <div className="p-4 border-b border-gray-100 dark:border-gray-800">
                      <h3 className="text-lg font-medium text-gray-800 dark:text-gray-100">Our Services</h3>
                    </div>
                    <div className="flex-1 overflow-y-auto p-4 custom-scrollbar">
                      <div className="space-y-1">
                        {services.map((service) => (
                          <button
                            key={service.id}
                            onClick={() => handleServiceSelect(service.id)}
                            className={`w-full text-left px-3 py-3 rounded-lg text-sm font-medium transition-colors duration-200 ${
                              activeService === service.id
                                ? "bg-[#e8f5e9] dark:bg-[#1b5e20]/30 text-[#6B7F3F] dark:text-[#8DA350]"
                                : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700/50"
                            }`}
                          >
                            {service.title}
                          </button>
                        ))}
                      </div>
                    </div>
                  </div>
                </SheetContent>
              </Sheet>
            </div>

            <p className="text-gray-600 dark:text-gray-300 mb-12 max-w-3xl">
              At Gentle Hearts Home Care, we offer a comprehensive range of personalized care services designed to meet
              your unique needs and enhance your quality of life in the comfort of your own home. Each service is
              delivered with care, professionalism, and a commitment to improving quality of life.
            </p>

            <div className="grid md:grid-cols-12 gap-8">
              {/* Side Navigation - Desktop */}
              <div className="md:col-span-3 hidden md:block">
                <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm p-4 rounded-xl shadow-md border border-gray-100 dark:border-gray-700 sticky top-24">
                  <h3 className="text-lg font-medium text-gray-800 dark:text-gray-100 mb-4">Our Services</h3>
                  <div className="space-y-1 max-h-[calc(100vh-200px)] overflow-y-auto pr-1 custom-scrollbar">
                    {services.map((service) => (
                      <button
                        key={service.id}
                        onClick={() => handleServiceSelect(service.id)}
                        className={`w-full text-left px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${
                          activeService === service.id
                            ? "bg-[#e8f5e9] dark:bg-[#1b5e20]/30 text-[#6B7F3F] dark:text-[#8DA350]"
                            : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700/50"
                        }`}
                      >
                        {service.title}
                      </button>
                    ))}
                  </div>
                </div>
              </div>

              {/* Service Details */}
              <div className="md:col-span-9 col-span-12">
                <motion.div
                  key={currentService.id} // This ensures animation triggers when service changes
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                  className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm p-6 md:p-8 rounded-xl shadow-lg border border-gray-100 dark:border-gray-700"
                >
                  <div className="flex flex-col md:flex-row gap-6 md:items-center mb-8">
                    <div
                      className={`w-16 h-16 rounded-full flex items-center justify-center shrink-0`}
                      style={{ backgroundColor: currentService.bgColor }}
                    >
                      <currentService.icon className="h-8 w-8" style={{ color: currentService.color }} />
                    </div>
                    <div>
                      <Badge
                        className="mb-2"
                        style={{ backgroundColor: currentService.bgColor, color: currentService.color }}
                      >
                        Care Service
                      </Badge>
                      <h2 className="text-2xl font-medium text-gray-800 dark:text-gray-100">{currentService.title}</h2>
                      {currentService.subtitle && (
                        <p className="text-gray-600 dark:text-gray-400 mt-1">{currentService.subtitle}</p>
                      )}
                    </div>
                  </div>

                  <div className="mb-8">
                    <Image
                      src={currentService.image || "/placeholder.svg"}
                      alt={currentService.title}
                      width={600}
                      height={400}
                      className="w-full h-64 md:h-80 object-cover rounded-xl"
                    />
                  </div>

                  <div className="space-y-6">
                    <div>
                      <h3 className="text-xl font-medium text-gray-800 dark:text-gray-100 mb-4">About This Service</h3>
                      <p className="text-gray-600 dark:text-gray-300">{currentService.description}</p>
                    </div>

                    <div>
                      <h3 className="text-xl font-medium text-gray-800 dark:text-gray-100 mb-4">What's Included</h3>
                      <ul className="grid md:grid-cols-2 gap-2">
                        {currentService.benefits.map((benefit, idx) => (
                          <li key={idx} className="flex items-start gap-2">
                            <div className="w-5 h-5 rounded-full bg-primary-light flex items-center justify-center shrink-0 mt-0.5">
                              <svg
                                className="w-3 h-3 text-[#6B7F3F] dark:text-[#8DA350]"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                              >
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                              </svg>
                            </div>
                            <span className="text-gray-700 dark:text-gray-300">{benefit}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    {currentService.idealFor && (
                      <div>
                        <h3 className="text-xl font-medium text-gray-800 dark:text-gray-100 mb-4">Ideal For</h3>
                        <ul className="grid md:grid-cols-1 gap-2">
                          {currentService.idealFor.map((item, idx) => (
                            <li key={idx} className="flex items-start gap-2">
                              <div className="w-5 h-5 rounded-full bg-secondary-light flex items-center justify-center shrink-0 mt-0.5">
                                <svg
                                  className="w-3 h-3 text-[#C26E3E] dark:text-[#E08D5C]"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  stroke="currentColor"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M5 13l4 4L19 7"
                                  />
                                </svg>
                              </div>
                              <span className="text-gray-700 dark:text-gray-300">{item}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {currentService.keyBenefits && (
                      <div>
                        <h3 className="text-xl font-medium text-gray-800 dark:text-gray-100 mb-4">Key Benefits</h3>
                        <ul className="grid md:grid-cols-1 gap-2">
                          {currentService.keyBenefits.map((item, idx) => (
                            <li key={idx} className="flex items-start gap-2">
                              <div className="w-5 h-5 rounded-full bg-neutral flex items-center justify-center shrink-0 mt-0.5">
                                <svg
                                  className="w-3 h-3 text-[#757575] dark:text-[#9e9e9e]"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  stroke="currentColor"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M5 13l4 4L19 7"
                                  />
                                </svg>
                              </div>
                              <span className="text-gray-700 dark:text-gray-300">{item}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {currentService.addOns && (
                      <div>
                        <h3 className="text-xl font-medium text-gray-800 dark:text-gray-100 mb-4">Available Add-Ons</h3>
                        <ul className="grid md:grid-cols-1 gap-2">
                          {currentService.addOns.map((item, idx) => (
                            <li key={idx} className="flex items-start gap-2">
                              <div className="w-5 h-5 rounded-full bg-primary-light flex items-center justify-center shrink-0 mt-0.5">
                                <Sparkles className="w-3 h-3 text-[#6B7F3F] dark:text-[#8DA350]" />
                              </div>
                              <span className="text-gray-700 dark:text-gray-300">{item}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    <div className="flex flex-col sm:flex-row gap-4 pt-4">
                      <Button className="bg-primary hover:bg-primary-dark text-white">
                        <Link href="/consultation">Request This Service</Link>
                      </Button>
                      <Button variant="outline" className="border-gray-200 dark:border-gray-700">
                        <Link href="/contact">Contact Us</Link>
                      </Button>
                    </div>
                    <p className="text-gray-600 dark:text-gray-300">
                      For more information about our home care services in Houston or to schedule a free consultation,
                      please contact us:
                    </p>
                    <div className="mt-4 space-y-2">
                      <p className="text-gray-700">
                        <strong>Phone:</strong>{" "}
                        <a href="tel:+12819756263" className="text-blue-600 hover:underline">
                          (*************
                        </a>
                      </p>
                      <p className="text-gray-700">
                        <strong>Email:</strong>{" "}
                        <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
                          <EMAIL>
                        </a>
                      </p>
                      <p className="text-gray-700">
                        <strong>Address:</strong> 3003 Windchase Blvd, Apt 1204, Houston, TX
                      </p>
                      <p className="text-gray-700">
                        <strong>Service Area:</strong> Houston, TX and surrounding communities
                      </p>
                    </div>
                  </div>
                </motion.div>
              </div>
            </div>
          </motion.div>
        </div>
      </main>

      <Footer />
    </div>
  )
}
