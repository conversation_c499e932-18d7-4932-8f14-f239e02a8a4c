import type { Metadata } from "next"
import ServicesPageClient from "./services-client"

export const metadata: Metadata = {
  title: "Healthcare Services in Manvel, TX | Home Health, Personal Care & Hospice",
  description:
    "Comprehensive healthcare services by Illiana's Angel Healthcare including home health services, personal care, and hospice care. Serving Manvel, TX and surrounding areas with RN-led care.",
  keywords: [
    "home health services Manvel TX",
    "personal care services",
    "hospice care Manvel",
    "skilled nursing home care",
    "Illiana's Angel Healthcare",
    "Derdine Lapaix-Fontil RN",
    "healthcare services Houston area",
  ],
  alternates: {
    canonical: "/services",
  },
  openGraph: {
    title: "Healthcare Services in Manvel, TX | Home Health, Personal Care & Hospice",
    description:
      "Comprehensive healthcare services by Illiana's Angel Healthcare including home health services, personal care, and hospice care. Serving Manvel, TX and surrounding areas with RN-led care.",
    url: "https://illianasangel.com/services",
  },
}

export default function ServicesPage() {
  return <ServicesPageClient />
}
