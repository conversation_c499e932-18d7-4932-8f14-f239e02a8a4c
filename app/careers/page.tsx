"use client"

import type React from "react"

import { useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, Briefcase, Calendar, CheckCircle, Copy, GraduationCap, Heart, Mail } from "lucide-react"
import Header from "@/components/header"
import Footer from "@/components/footer"
import PageHero from "@/components/page-hero"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/hooks/use-toast"
import { Checkbox } from "@/components/ui/checkbox"

export default function CareersPage() {
  const [copied, setCopied] = useState(false)
  const [copiedEmail, setCopiedEmail] = useState("")
  const [isApplicationOpen, setIsApplicationOpen] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [selectedPosition, setSelectedPosition] = useState("")
  const { toast } = useToast()
  const [smsConsent, setSmsConsent] = useState(false)

  const copyEmail = (email: string) => {
    navigator.clipboard.writeText(email)
    setCopiedEmail(email)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  const handleApplicationSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setIsSubmitting(true)

    const formData = new FormData(e.currentTarget)
    formData.append("position", selectedPosition)
    formData.append("smsConsent", smsConsent.toString())

    try {
      const response = await fetch("/api/job-application", {
        method: "POST",
        body: formData,
      })

      if (response.ok) {
        toast({
          title: "Application Submitted",
          description: "Thank you for your application. We'll be in touch soon!",
        })
        setIsApplicationOpen(false)
        e.currentTarget.reset()
        setSmsConsent(false)
      } else {
        throw new Error("Failed to submit application")
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to submit application. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Animation variants
  const fadeIn = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.6 } },
  }

  const containerClass = "px-4 md:px-8 lg:px-16 xl:px-24 2xl:px-32 mx-auto max-w-[1600px]"

  // Career benefits data
  const careerBenefits = [
    {
      title: "Competitive Compensation",
      description: "We offer industry-leading pay rates and benefits packages",
      icon: Briefcase,
      color: "#e05691",
    },
    {
      title: "Flexible Scheduling",
      description: "Work hours that fit your lifestyle and personal commitments",
      icon: Calendar,
      color: "#001a35",
    },
    {
      title: "Career Growth",
      description: "Opportunities for advancement and professional development",
      icon: GraduationCap,
      color: "#757575",
    },
    {
      title: "Supportive Environment",
      description: "Join a team that values your contributions and well-being",
      icon: Heart,
      color: "#e05691",
    },
  ]

  // Job postings data - Updated for Journey of Care
  const jobPostings = [
    {
      title: "Certified Home Health Aide (HHA)",
      location: "North Houston, Conroe, The Woodlands, Spring, and surrounding areas.",
      type: "Full-Time / Part-Time",
      badgeColor: "#fce7f3",
      badgeTextColor: "#be185d",
      descriptions: [
        "We are seeking compassionate and skilled Certified Home Health Aides to join our team at Journey of Care. As an HHA, you will provide quality personal care to clients in their homes throughout North Houston, Conroe, The Woodlands, Spring, and surrounding areas, helping them maintain their health and independence.",
      ],
      responsibilities: [
        "Assist clients with activities of daily living (bathing, dressing, grooming)",
        "Take and record vital signs accurately",
        "Assist with mobility, transfers, and positioning",
        "Provide companionship and emotional support",
        "Help with medication reminders (no administration)",
        "Maintain client hygiene and comfort",
        "Document care provided and report changes in condition",
        "Communicate effectively with nursing staff and families",
      ],
      qualifications: [
        "Current HHA certification in Texas",
        "High school diploma or equivalent",
        "Prior experience as an HHA (1+ year preferred)",
        "Valid driver's license and reliable transportation",
        "Compassionate and patient demeanor",
        "Ability to pass a background check and drug screen",
        "Physical ability to assist clients with mobility",
        "CPR certification preferred",
      ],
      email: "<EMAIL>",
    },
    {
      title: "Personal Care Assistant",
      location: "North Houston, Conroe, The Woodlands, Spring, and surrounding areas.",
      type: "Full-Time / Part-Time",
      badgeColor: "#fce7f3",
      badgeTextColor: "#be185d",
      descriptions: [
        "We are seeking compassionate and reliable Personal Care Assistants to join our team at Journey of Care. As a Personal Care Assistant, you will provide personal care and assistance to clients in their homes throughout North Houston, Conroe, The Woodlands, Spring, and surrounding areas, helping them maintain their independence and quality of life.",
      ],
      responsibilities: [
        "Assist clients with personal care (bathing, grooming, dressing)",
        "Provide companionship and emotional support",
        "Help with light housekeeping and meal preparation",
        "Assist with mobility and transfers",
        "Provide medication reminders",
        "Accompany clients to appointments when needed",
        "Monitor client safety and report concerns",
        "Maintain accurate documentation of care provided",
      ],
      qualifications: [
        "High school diploma or equivalent",
        "Personal Care Assistant certification preferred",
        "Previous caregiving experience preferred but not required",
        "Valid driver's license and reliable transportation",
        "Ability to pass a background check and drug screen",
        "Strong communication skills",
        "Compassionate and patient demeanor",
        "Physical ability to assist clients with daily activities",
      ],
      email: "<EMAIL>",
    },
    {
      title: "Companion Caregiver",
      location: "North Houston, Conroe, The Woodlands, Spring, and surrounding areas.",
      type: "Full-Time / Part-Time",
      badgeColor: "#fce7f3",
      badgeTextColor: "#be185d",
      descriptions: [
        "We are looking for dedicated Companion Caregivers to join our healthcare team at Journey of Care. As a Companion Caregiver, you will provide meaningful companionship and light assistance to our clients in their homes throughout North Houston, Conroe, The Woodlands, Spring, and surrounding areas, focusing on social engagement and emotional support.",
      ],
      responsibilities: [
        "Provide friendly conversation and social engagement",
        "Assist with light housekeeping and meal preparation",
        "Help with errands and transportation to appointments",
        "Engage in recreational activities (games, crafts, walks)",
        "Provide technology assistance and communication support",
        "Monitor client well-being and safety",
        "Document activities and report any concerns",
        "Maintain professional boundaries while building rapport",
      ],
      qualifications: [
        "High school diploma or equivalent",
        "Previous companion care or customer service experience preferred",
        "Excellent communication and interpersonal skills",
        "Valid driver's license and reliable transportation",
        "Ability to pass a background check and drug screen",
        "Patient, kind, and empathetic personality",
        "Ability to engage with seniors and individuals with disabilities",
        "Basic computer skills helpful",
      ],
      email: "<EMAIL>",
    },
    {
      title: "Respite Care Provider",
      location: "North Houston, Conroe, The Woodlands, Spring, and surrounding areas.",
      type: "Part-Time / As Needed",
      badgeColor: "#fce7f3",
      badgeTextColor: "#be185d",
      descriptions: [
        "We are seeking reliable Respite Care Providers to join our team at Journey of Care. As a Respite Care Provider, you will provide temporary relief care for family caregivers throughout North Houston, Conroe, The Woodlands, Spring, and surrounding areas, ensuring continuity of care while giving families the break they need.",
      ],
      responsibilities: [
        "Provide temporary care relief for family caregivers",
        "Maintain established care routines and schedules",
        "Assist with personal care and daily living activities",
        "Provide companionship and emotional support",
        "Monitor client safety and well-being",
        "Follow established care plans and protocols",
        "Communicate effectively with families and care team",
        "Be available for flexible scheduling including evenings and weekends",
      ],
      qualifications: [
        "High school diploma or equivalent",
        "Previous caregiving or healthcare experience preferred",
        "Ability to work flexible hours including evenings and weekends",
        "Valid driver's license and reliable transportation",
        "Ability to pass a background check and drug screen",
        "Strong communication and organizational skills",
        "Compassionate and reliable personality",
        "Ability to handle emergency situations calmly",
      ],
      email: "<EMAIL>",
    },
  ]

  // FAQ data
  const faqs = [
    {
      question: "What qualifications do I need to work at Journey of Care?",
      answer:
        "Requirements vary by position. For certified roles, you need current Texas certification and relevant experience. For companion and personal care positions, certification is preferred but we provide training. All positions require a valid driver's license, reliable transportation, and the ability to pass background checks.",
    },
    {
      question: "What is the application process like?",
      answer:
        "Our application process involves submitting your resume and cover letter, followed by a phone screening. Qualified candidates are invited for an in-person interview. If selected, you'll complete background checks, drug screening, and our comprehensive orientation program.",
    },
    {
      question: "Do you offer full-time and part-time positions?",
      answer:
        "Yes, we offer both full-time and part-time positions to accommodate different schedules. We work with our healthcare professionals to create schedules that work for both them and our clients throughout Conroe, TX and surrounding areas.",
    },
    {
      question: "What kind of training do you provide?",
      answer:
        "We provide comprehensive initial training covering care protocols, safety procedures, documentation requirements, and client communication. We also offer ongoing education opportunities to help our staff stay current with best practices in home care.",
    },
    {
      question: "What areas do you serve?",
      answer:
        "We currently serve Serving North Houston, Conroe, TheWoodlands, Spring, and surrounding areas Please contact us for specific information about service locations and opportunities in your area.",
    },
  ]

  return (
    <div className="min-h-screen font-sans bg-white dark:bg-gray-950 overflow-hidden transition-colors duration-300">
      {/* Gradient Orbs */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
        <div className="absolute -top-40 -right-40 w-96 h-96 rounded-full bg-gradient-to-br from-primary/10 to-primary-light/5 dark:from-primary/5 dark:to-primary-light/2 blur-3xl"></div>
        <div className="absolute top-1/3 -left-40 w-80 h-80 rounded-full bg-gradient-to-tr from-secondary/10 to-secondary-light/5 dark:from-secondary/5 dark:to-secondary-light/2 blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-72 h-72 rounded-full bg-gradient-to-r from-[#757575]/10 to-[#9e9e9e]/5 dark:from-[#757575]/5 dark:to-[#9e9e9e]/2 blur-3xl"></div>
      </div>

      <Header />

      {/* Hero Section */}
      <PageHero
        title="Join Our Team"
        subtitle="Make a difference in people's lives every day"
        imageSrc="/images/team.png"
        imageAlt="Healthcare professionals working together"
      />

      {/* Main Content */}
      <main className="py-16 relative z-10">
        <div className={containerClass}>
          <div className="mb-8">
            <Link
              href="/"
              className="inline-flex items-center text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary-light transition-colors duration-200"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Home
            </Link>
          </div>

          <motion.div initial="hidden" animate="visible" variants={fadeIn} className="max-w-5xl mx-auto">
            <h1 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">
              Join Our Compassionate Team at Journey of Care
            </h1>

            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-12">
              Make a meaningful difference in people's lives. We're looking for compassionate, dedicated home care
              professionals to join our team at Journey of Care in Serving North Houston, Conroe, TheWoodlands, Spring, and surrounding areas
            </p>

            {/* Career Benefits Section */}
            <div className="grid md:grid-cols-2 gap-8 mb-16">
              <motion.div variants={fadeIn} className="space-y-6">
                <h2 className="text-2xl font-light text-gray-800 dark:text-gray-100">
                  Why Choose a Career with Journey of Care?
                </h2>
                <p className="text-gray-600 dark:text-gray-300">
                  At Journey of Care, we value our home care professionals and recognize the essential role they
                  play in our clients' lives. We're committed to creating a supportive, rewarding work environment where
                  you can grow professionally while making a meaningful impact in the North Houston, Conroe, The Woodlands, Spring, and surrounding areas.
                </p>

                <div className="space-y-6 mt-8">
                  {careerBenefits.map((benefit, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5, delay: 0.2 + index * 0.1 }}
                      className="flex gap-4"
                    >
                      <div className="w-12 h-12 rounded-full bg-white dark:bg-gray-800 shadow-md flex items-center justify-center flex-shrink-0">
                        <benefit.icon className="h-6 w-6" style={{ color: benefit.color }} />
                      </div>
                      <div>
                        <h4 className="text-lg font-medium text-gray-800 dark:text-gray-100 mb-1">{benefit.title}</h4>
                        <p className="text-gray-600 dark:text-gray-300">{benefit.description}</p>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </motion.div>

              <motion.div variants={fadeIn} className="relative">
                <Card className="overflow-hidden border-none shadow-xl dark:shadow-gray-900/20 rounded-2xl h-full">
                  <CardContent className="p-0 h-full">
                    <Image
                      src="https://res.cloudinary.com/dvauarkh6/image/upload/v1747925848/pexels-cedric-fauntleroy-4266944_vob3wj.jpg"
                      alt="Healthcare professional helping a client"
                      width={500}
                      height={600}
                      className="object-cover w-full h-full"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent flex flex-col justify-end p-8">
                      <Badge className="mb-3 bg-white/90 text-primary hover:bg-white/90 w-fit">Now Hiring</Badge>
                      <h3 className="text-2xl font-medium text-white mb-2">Home Care Professionals</h3>
                      <p className="text-white/90 mb-4">
                        Join our team of compassionate home care professionals and make a difference in people's lives
                        every day.
                      </p>
                      <Button
                        className="bg-primary hover:bg-primary-dark text-white w-fit"
                        onClick={() => {
                          setSelectedPosition("General Application")
                          setIsApplicationOpen(true)
                        }}
                      >
                        Apply Now
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </div>

            {/* Current Openings Section */}
            <motion.div variants={fadeIn} className="mb-16">
              <h2 className="text-2xl font-light text-gray-800 dark:text-gray-100 mb-6">Current Openings</h2>

              <div className="space-y-6">
                {jobPostings.map((job, index) => (
                  <Card
                    key={index}
                    className="overflow-hidden border border-gray-100 dark:border-gray-800 shadow-md bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl"
                  >
                    <CardContent className="p-6">
                      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-6">
                        <div>
                          <Badge
                            className="mb-2"
                            style={{
                              backgroundColor: job.badgeColor,
                              color: job.badgeTextColor,
                            }}
                          >
                            {job.type}
                          </Badge>
                          <h3 className="text-xl font-medium text-gray-800 dark:text-gray-100">{job.title}</h3>
                          <p className="text-gray-600 dark:text-gray-300 text-sm">{job.location}</p>
                        </div>
                        <Button
                          className="bg-primary hover:bg-primary-dark text-white"
                          onClick={() => {
                            setSelectedPosition(job.title)
                            setIsApplicationOpen(true)
                          }}
                        >
                          Apply Now
                        </Button>
                      </div>

                      <div className="space-y-4 mb-6">
                        <h4 className="font-medium text-gray-800 dark:text-gray-100">Job Description:</h4>
                        {job.descriptions.map((desc, i) => (
                          <p key={i} className="text-gray-600 dark:text-gray-300">
                            {desc}
                          </p>
                        ))}

                        <h4 className="font-medium text-gray-800 dark:text-gray-100 mt-4">Responsibilities:</h4>
                        <ul className="list-disc pl-5 space-y-2 text-gray-600 dark:text-gray-300">
                          {job.responsibilities.map((responsibility, i) => (
                            <li key={i}>{responsibility}</li>
                          ))}
                        </ul>

                        <h4 className="font-medium text-gray-800 dark:text-gray-100 mt-4">Qualifications:</h4>
                        <ul className="list-disc pl-5 space-y-2 text-gray-600 dark:text-gray-300">
                          {job.qualifications.map((qualification, i) => (
                            <li key={i}>{qualification}</li>
                          ))}
                        </ul>
                      </div>

                      <div className="bg-gray-50 dark:bg-gray-700/30 p-4 rounded-lg">
                        <h4 className="font-medium text-gray-800 dark:text-gray-100 mb-3">How to Apply:</h4>
                        <p className="text-gray-600 dark:text-gray-300 mb-4">
                          To apply for this position, please send your resume and a cover letter to the email address
                          below. In the subject line, please include "{job.title} Application - [Your Name]".
                        </p>

                        <div className="flex items-center gap-2">
                          <div className="flex items-center gap-2 bg-white dark:bg-gray-800 px-3 py-2 rounded-lg flex-grow">
                            <Mail className="h-5 w-5 text-primary dark:text-primary-light" />
                            <span className="text-gray-700 dark:text-gray-300"><EMAIL></span>
                          </div>
                          <Button
                            variant="outline"
                            size="icon"
                            className="border-gray-200 dark:border-gray-700 bg-transparent"
                            onClick={() => copyEmail(job.email)}
                          >
                            {copied && copiedEmail === job.email ? (
                              <CheckCircle className="h-4 w-4 text-primary" />
                            ) : (
                              <Copy className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </motion.div>

            {/* FAQ Section */}
            <motion.div variants={fadeIn} className="mb-16">
              <h2 className="text-2xl font-light text-gray-800 dark:text-gray-100 mb-6">Frequently Asked Questions</h2>

              <Accordion type="single" collapsible className="space-y-4">
                {faqs.map((faq, index) => (
                  <AccordionItem
                    key={index}
                    value={`item-${index}`}
                    className="border border-gray-200 dark:border-gray-700 rounded-xl overflow-hidden shadow-sm bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm"
                  >
                    <AccordionTrigger className="px-6 py-4 hover:no-underline text-gray-800 dark:text-gray-100 font-medium text-left data-[state=open]:text-primary dark:data-[state=open]:text-primary-light">
                      {faq.question}
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-4 text-gray-600 dark:text-gray-300">
                      {faq.answer}
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </motion.div>

            {/* CTA Section */}
            <motion.div
              variants={fadeIn}
              className="bg-primary/10 dark:bg-primary/20 p-8 rounded-xl border border-primary/10 dark:border-primary-light/10 text-center"
            >
              <h2 className="text-2xl font-light text-gray-800 dark:text-gray-100 mb-4">Ready to Make a Difference?</h2>
              <p className="text-gray-700 dark:text-gray-300 mb-6 max-w-2xl mx-auto">
                Join our team of dedicated home care professionals at Journey of Care and start making a positive
                impact in people's lives throughout North Houston, Conroe, The Woodlands, Spring, and surrounding areas. We look forward to hearing from you!
              </p>
              <div className="space-y-4">
                <Button
                  className="bg-primary hover:bg-primary-dark text-white px-8"
                  onClick={() => {
                    setSelectedPosition("General Application")
                    setIsApplicationOpen(true)
                  }}
                >
                  Apply Today
                </Button>
                <div className="text-sm text-gray-600 dark:text-gray-300">
                  <p>
                    <strong>Contact Information:</strong>
                  </p>
                  <p>Phone: (*************</p>
                  <p>Email: <EMAIL></p>
                  <p>Serving: North Houston, Conroe, The Woodlands, Spring, and surrounding areas.</p>
                </div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </main>

      {/* Job Application Dialog */}
      <Dialog open={isApplicationOpen} onOpenChange={setIsApplicationOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto z-[9999]">
          <DialogHeader>
            <DialogTitle className="text-2xl font-light text-gray-800">Apply for {selectedPosition}</DialogTitle>
            <DialogDescription className="text-gray-600">
              Fill out the form below to submit your application. We'll review your information and get back to you
              soon.
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleApplicationSubmit} className="space-y-6 mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName">First Name *</Label>
                <Input id="firstName" name="firstName" required />
              </div>
              <div className="space-y-2">
                <Label htmlFor="lastName">Last Name *</Label>
                <Input id="lastName" name="lastName" required />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">Email Address *</Label>
              <Input id="email" name="email" type="email" required />
            </div>

            <div className="space-y-2">
              <Label htmlFor="phone">Phone Number *</Label>
              <Input id="phone" name="phone" type="tel" required />
            </div>

            <div className="space-y-2">
              <Label htmlFor="address">Address</Label>
              <Input id="address" name="address" placeholder="Street Address" />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="city">City</Label>
                <Input id="city" name="city" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="state">State</Label>
                <Input id="state" name="state" placeholder="TX" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="zipCode">ZIP Code</Label>
                <Input id="zipCode" name="zipCode" />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="experience">Years of Experience</Label>
              <Input id="experience" name="experience" placeholder="e.g., 2 years" />
            </div>

            <div className="space-y-2">
              <Label htmlFor="availability">Availability</Label>
              <Input id="availability" name="availability" placeholder="e.g., Full-time, Part-time, Weekends" />
            </div>

            <div className="space-y-2">
              <Label htmlFor="resume">Resume * (PDF, DOC, DOCX - Max 5MB)</Label>
              <Input
                id="resume"
                name="resume"
                type="file"
                accept=".pdf,.doc,.docx"
                required
                className="file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-white hover:file:bg-primary-dark"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="coverLetter">Cover Letter / Additional Information</Label>
              <Textarea
                id="coverLetter"
                name="coverLetter"
                placeholder="Tell us why you're interested in this position and what makes you a great fit..."
                rows={4}
              />
            </div>

            {/* SMS Consent Checkbox */}
            <div className="space-y-3 p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700">
              <div className="flex items-start space-x-3">
                <Checkbox
                  id="smsConsent"
                  checked={smsConsent}
                  onCheckedChange={(checked) => setSmsConsent(checked as boolean)}
                  className="mt-1"
                />
                <label htmlFor="smsConsent" className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
                  By checking this box, I consent to receive text messages related to job application updates, interview
                  scheduling, and employment communications from Journey of Care. You can reply "STOP" at any time
                  to opt out. Message and data rates may apply. Message frequency may vary, text HELP to (*************
                  for assistance. For more information, please refer to our{" "}
                  <Link
                    href="/privacy-policy"
                    className="text-[#1e3a8a] hover:text-[#1e40af] font-bold hover:underline"
                  >
                    privacy policy
                  </Link>
                  , and{" "}
                  <Link
                    href="/terms-conditions"
                    className="text-[#1e3a8a] hover:text-[#1e40af] font-bold hover:underline"
                  >
                    Terms and Conditions
                  </Link>{" "}
                  on our website.
                </label>
              </div>
            </div>

            <div className="flex gap-4 pt-4">
              <Button type="button" variant="outline" onClick={() => setIsApplicationOpen(false)} className="flex-1">
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="flex-1 bg-primary hover:bg-primary-dark text-white"
              >
                {isSubmitting ? "Submitting..." : "Submit Application"}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      <Footer />
    </div>
  )
}
