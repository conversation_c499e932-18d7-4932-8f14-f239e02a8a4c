"use client"

import { useEffect, useState, useRef } from "react"
import Image from "next/image"
import Link from "next/link"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Heart,
  Users,
  Home,
  Shield,
  Clock,
  Award,
  Smile,
  HeartHandshake,
  CheckCircle2,
  AlarmClock,
  UserPlus,
  GraduationCap,
  HelpingHand,
  Utensils,
  Moon,
  ChevronRight,
  MapPin,
  Phone,
  Mail,
  Send,
  CheckCircle,
  Printer,
} from "lucide-react"
import { useInView } from "react-intersection-observer"
import { cn } from "@/lib/utils"
import { useTheme } from "next-themes"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import Header from "@/components/header"
import Footer from "@/components/footer"

export default function HomePage() {
  const [scrolled, setScrolled] = useState(false)
  const [mounted, setMounted] = useState(false)
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const { setTheme, theme } = useTheme()
  const menuRef = useRef<HTMLDivElement>(null)

  // Make sure the component is mounted before accessing theme
  useEffect(() => {
    setMounted(true)
  }, [])

  // Handle scroll effect for header
  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 20)
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  // Close menu when clicking outside
  useEffect(() => {
    if (!mobileMenuOpen) return

    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setMobileMenuOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    document.body.style.overflow = "hidden"

    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
      document.body.style.overflow = ""
    }
  }, [mobileMenuOpen])

  // Simplified animation variants
  const fadeIn = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.6 } },
  }

  const fadeInLeft = {
    hidden: { opacity: 0, x: -20 },
    visible: { opacity: 1, x: 0, transition: { duration: 0.6 } },
  }

  const fadeInRight = {
    hidden: { opacity: 0, x: 20 },
    visible: { opacity: 1, x: 0, transition: { duration: 0.6 } },
  }

  // Simplified intersection observer hooks
  const { ref: heroRef, inView: heroInView } = useInView({ triggerOnce: true, threshold: 0.1 })
  const { ref: servicesRef, inView: servicesInView } = useInView({ triggerOnce: true, threshold: 0.1 })
  const { ref: aboutRef, inView: aboutInView } = useInView({ triggerOnce: true, threshold: 0.1 })
  const { ref: missionRef, inView: missionInView } = useInView({ triggerOnce: true, threshold: 0.1 })
  const { ref: contactRef, inView: contactInView } = useInView({ triggerOnce: true, threshold: 0.1 })
  const { ref: faqRef, inView: faqInView } = useInView({ triggerOnce: true, threshold: 0.1 })

  // Custom container class with increased margins on desktop
  const containerClass = "px-4 md:px-8 lg:px-16 xl:px-24 2xl:px-32 mx-auto max-w-[1600px]"

  // Services data
  const services = [
    {
      id: "emergency-assistance",
      title: "24-Hour Emergency Assistance",
      description: "We offer immediate assistance around the clock during emergencies, requiring no specialized tools.",
      icon: AlarmClock,
      color: "#2e7d32",
      bgColor: "#e8f5e9",
    },
    {
      id: "adult-companion",
      title: "Adult Companion Services",
      description:
        "Our services focus on fostering social interaction and companionship for adults, particularly those on the Elderly Waiver (EW).",
      icon: UserPlus,
      color: "#ff9800",
      bgColor: "#fff8e1",
    },
    {
      id: "home-support-training",
      title: "Individualized Home Support with Training",
      description:
        "We provide tailored support aimed at enhancing independent living skills within the individual's home.",
      icon: GraduationCap,
      color: "#757575",
      bgColor: "#f5f5f5",
    },
    {
      id: "home-support",
      title: "Individualized Home Support without Training",
      description: "Our team helps with everyday tasks to ensure a safe and comfortable living environment.",
      icon: HelpingHand,
      color: "#2e7d32",
      bgColor: "#e8f5e9",
    },
    {
      id: "family-training",
      title: "Personalized Home Support with Family Training",
      description:
        "Our direct support staff equip family members with the skills needed to better assist their loved ones at home.",
      icon: Users,
      color: "#ff9800",
      bgColor: "#fff8e1",
    },
    {
      id: "homemaking",
      title: "Homemaking Services",
      description: "We assist with light housekeeping, meal preparation, and laundry to create a safe and clean home.",
      icon: Utensils,
      color: "#757575",
      bgColor: "#f5f5f5",
    },
    {
      id: "community-living",
      title: "Independent Community Living Support",
      description: "We facilitate support for daily activities and encourage participation in community events.",
      icon: MapPin,
      color: "#2e7d32",
      bgColor: "#e8f5e9",
    },
    {
      id: "night-supervision",
      title: "Night Supervision",
      description:
        "Our night supervision services include overnight care to ensure safety and assistance during the night.",
      icon: Moon,
      color: "#ff9800",
      bgColor: "#fff8e1",
    },
    {
      id: "respite-care",
      title: "In-home Respite Care",
      description:
        "We provide temporary relief for primary caregivers, offering them a break while ensuring quality care at home.",
      icon: HeartHandshake,
      color: "#757575",
      bgColor: "#f5f5f5",
    },
  ]

  // Contact information
  const contactInfo = [
    {
      label: "Phone",
      value: "+****************",
      icon: Phone,
    },
    {
      label: "Email",
      value: "<EMAIL>",
      icon: Mail,
    },
    {
      label: "Fax",
      value: "+****************",
      icon: Printer,
    },
    {
      label: "Address",
      value: "309 Dover Heights Trl, Mansfield, TX 76063",
      icon: MapPin,
    },
    {
      label: "NPI",
      value: "**********",
      icon: CheckCircle,
    },
  ]

  // FAQ data
  const faqs = [
    {
      question: "What services do you offer?",
      answer:
        "We offer a comprehensive range of home healthcare services including 24-hour emergency assistance, adult companion services, personalized home support with and without training, homemaking services, night supervision, and in-home respite care. All our services are tailored to meet the unique needs of each client.",
    },
    {
      question: "How do I know if my loved one needs home care?",
      answer:
        "Signs that indicate your loved one might need home care include difficulty with daily activities (bathing, dressing, meal preparation), medication management issues, mobility challenges, social isolation, or a recent hospital stay. We offer free consultations to assess needs and determine the most appropriate care plan.",
    },
    {
      question: "Are your caregivers licensed and insured?",
      answer:
        "Yes, all our caregivers are licensed, insured, and undergo comprehensive background checks. They also receive ongoing training to ensure they provide the highest quality of care. We are fully compliant with all state and federal regulations governing home healthcare providers.",
    },
    {
      question: "How do you match caregivers with clients?",
      answer:
        "We match caregivers with clients based on several factors including required skills, personality compatibility, scheduling needs, and specific care requirements. We believe the relationship between caregiver and client is crucial for successful care, so we take great care in finding the right match.",
    },
    {
      question: "What are your rates and do you accept insurance?",
      answer:
        "Our rates vary depending on the type and level of care needed. We accept long-term care insurance, private pay, and in some cases, Medicare and Medicaid. Our team will work with you to determine coverage eligibility and explore all payment options to ensure you receive the care you need.",
    },
    {
      question: "How quickly can services start?",
      answer:
        "In most cases, we can initiate services within 24-48 hours of an initial assessment. For urgent situations, we strive to provide care as quickly as possible. The timeline depends on the complexity of care needed and scheduling availability.",
    },
    {
      question: "What areas do you serve?",
      answer:
        "We currently serve the Dallas/Fort Worth area in Texas. Please contact us for specific information about service in your location. We're continuously expanding our service area to meet the growing demand for quality home healthcare services.",
    },
  ]

  return (
    <div className="min-h-screen font-sans bg-white dark:bg-gray-950 overflow-x-hidden transition-colors duration-300">
      {/* Gradient Orbs */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
        <div className="absolute -top-40 -right-40 w-96 h-96 rounded-full bg-gradient-to-br from-[#2e7d32]/10 to-[#4caf50]/5 dark:from-[#2e7d32]/5 dark:to-[#4caf50]/2 blur-3xl"></div>
        <div className="absolute top-1/3 -left-40 w-80 h-80 rounded-full bg-gradient-to-tr from-[#ff9800]/10 to-[#ffb74d]/5 dark:from-[#ff9800]/5 dark:to-[#ffb74d]/2 blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-72 h-72 rounded-full bg-gradient-to-r from-[#757575]/10 to-[#9e9e9e]/5 dark:from-[#757575]/5 dark:to-[#9e9e9e]/2 blur-3xl"></div>
      </div>

      <Header />

      {/* Hero Section */}
      <section ref={heroRef} className={cn(containerClass, "pt-32 pb-16 md:pt-40 md:pb-24 relative z-10")}>
        <motion.div
          initial="hidden"
          animate={heroInView ? "visible" : "hidden"}
          variants={fadeIn}
          className="grid md:grid-cols-2 gap-12 items-center"
        >
          <div>
            <h1 className="text-4xl md:text-5xl font-light leading-tight text-gray-800 dark:text-gray-100 tracking-tight">
              Compassionate care tailored to your needs, delivered in the comfort of
              <span className="relative inline-block ml-2">
                your home
                <span className="absolute -right-2 -top-1">
                  <Heart className="h-6 w-6 text-[#ff9800]" />
                  <Home className="h-6 w-6 text-[#2e7d32] dark:text-[#4caf50] absolute -top-1 -right-4" />
                </span>
              </span>
            </h1>
            <p className="mt-6 text-gray-600 dark:text-gray-300 text-lg font-light">
              Bringing comfort and compassionate care to your home.
            </p>
            <div>
              <Button className="mt-8 rounded-full bg-gray-800 hover:bg-gray-700 dark:bg-gray-700 dark:hover:bg-gray-600 text-white px-6 py-6 h-auto flex items-center gap-2 transition-all duration-300 hover:shadow-lg hover:gap-3">
                <Link href="/consultation">Book a FREE consultation</Link>
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M8 0L6.59 1.41L12.17 7H0V9H12.17L6.59 14.59L8 16L16 8L8 0Z" fill="white" />
                </svg>
              </Button>
            </div>
          </div>

          <div className="relative">
            <Card className="overflow-hidden border-none shadow-xl dark:shadow-gray-900/20 transform transition-transform duration-500 hover:scale-[1.02] bg-white/40 dark:bg-gray-900/40 backdrop-blur-sm rounded-2xl">
              <CardContent className="p-0">
                <Image
                  src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/pexels-olly-3768146%20%281%29.jpg-iEtmpR0Yj5zzCzzHxhXaQugfGLfWWF.jpeg"
                  alt="Caregiver and elderly woman baking together in kitchen"
                  width={800}
                  height={600}
                  className="object-cover w-full h-full"
                />
              </CardContent>
            </Card>

            <div className="absolute top-4 left-4 flex flex-col gap-2">
              {[
                { text: "Support", icon: Shield, color: "bg-[#2e7d32]/90 dark:bg-[#2e7d32]/80" },
                { text: "Compassion", icon: Users, color: "bg-[#ff9800]/90 dark:bg-[#ff9800]/80" },
                { text: "Healing", icon: Heart, color: "bg-gray-700/90 dark:bg-gray-600/80" },
              ].map((badge, index) => (
                <div key={badge.text}>
                  <Badge
                    className={`${badge.color} hover:${badge.color} text-white px-4 py-2 rounded-full transition-all duration-300 hover:shadow-md backdrop-blur-sm`}
                  >
                    <badge.icon className="h-4 w-4 mr-1" /> {badge.text}
                  </Badge>
                </div>
              ))}
            </div>

            <div className="absolute -bottom-4 -right-4 bg-white/80 dark:bg-gray-800/80 backdrop-blur-md p-4 rounded-xl shadow-lg border border-gray-100 dark:border-gray-700">
              <div className="text-[#2e7d32] dark:text-[#4caf50] font-bold text-3xl">200+</div>
              <div className="text-gray-600 dark:text-gray-300 text-sm">Satisfied Clients</div>
              <div className="mt-2 flex -space-x-2">
                {[300, 400, 500, 600].map((shade, i) => (
                  <div
                    key={i}
                    className={`w-8 h-8 rounded-full bg-gray-${shade} dark:bg-gray-${shade - 100} border-2 border-white dark:border-gray-800`}
                  />
                ))}
              </div>
              <div className="mt-2 bg-[#2e7d32]/90 dark:bg-[#2e7d32]/80 backdrop-blur-sm text-white text-xs px-3 py-1 rounded-full inline-flex items-center">
                <svg
                  className="w-3 h-3 mr-1"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fillRule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clipRule="evenodd"
                  ></path>
                </svg>
                Professional Caregivers
              </div>
            </div>
          </div>
        </motion.div>
      </section>

      {/* Services Section */}
      <section
        ref={servicesRef}
        className="py-24 relative z-10 overflow-hidden bg-gradient-to-b from-white to-[#f9f9f9] dark:from-gray-950 dark:to-gray-900"
        id="services"
      >
        <motion.div
          initial="hidden"
          animate={servicesInView ? "visible" : "hidden"}
          variants={fadeIn}
          className={cn(containerClass)}
        >
          <div className="text-center mb-16">
            <div className="inline-block">
              <Badge className="px-4 py-1.5 bg-[#e8f5e9] dark:bg-[#1b5e20]/30 text-[#2e7d32] dark:text-[#4caf50] rounded-full text-sm font-medium mb-4">
                Our Services
              </Badge>
            </div>
            <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-4">
              Comprehensive Care Services
            </h2>
            <div className="w-20 h-1 bg-[#ff9800] mx-auto rounded-full mb-6"></div>
            <p className="max-w-2xl mx-auto text-gray-600 dark:text-gray-300 text-lg font-light">
              We offer a wide range of personalized care services designed to meet your unique needs and enhance your
              quality of life.
            </p>
          </div>

          {/* Service cards */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
            {services.map((service, index) => (
              <div key={index} className="group">
                <Card className="h-full overflow-hidden border border-gray-100 dark:border-gray-800 shadow-md hover:shadow-xl transition-all duration-500 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl">
                  <CardContent className="p-6 flex flex-col h-full">
                    <div
                      className="w-16 h-16 rounded-full flex items-center justify-center mb-5 transition-all duration-300 group-hover:scale-110"
                      style={{ backgroundColor: service.bgColor }}
                    >
                      <service.icon className="h-8 w-8" style={{ color: service.color }} />
                    </div>

                    <h3 className="text-xl font-medium text-gray-800 dark:text-gray-100 mb-3 group-hover:text-[#2e7d32] dark:group-hover:text-[#4caf50] transition-colors duration-300">
                      {service.title}
                    </h3>

                    <p className="text-gray-600 dark:text-gray-300 font-light mb-5 flex-grow">{service.description}</p>

                    <div className="mt-auto">
                      <Link
                        href={`/services?service=${service.id}`}
                        className="inline-flex items-center text-sm font-medium text-[#2e7d32] dark:text-[#4caf50] hover:text-[#1b5e20] dark:hover:text-[#66bb6a] transition-colors duration-300"
                      >
                        Learn more
                        <ChevronRight className="ml-1 h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
                      </Link>
                    </div>
                  </CardContent>
                </Card>
              </div>
            ))}
          </div>

          <div className="mt-16 text-center">
            <div className="relative inline-block">
              <Button className="rounded-full bg-[#ff9800] hover:bg-[#f57c00] text-white px-8 py-6 h-auto transition-all duration-300 hover:shadow-lg">
                <Link href="/consultation">Schedule a Service Consultation</Link>
              </Button>

              <div className="absolute -top-10 -right-10">
                <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm shadow-lg rounded-full p-2 flex items-center justify-center">
                  <Heart className="h-6 w-6 text-[#ff9800]" />
                </div>
              </div>

              <div className="absolute -bottom-8 -left-8">
                <div className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm shadow-lg rounded-full p-2 flex items-center justify-center">
                  <Shield className="h-6 w-6 text-[#2e7d32] dark:text-[#4caf50]" />
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Decorative elements */}
        <div className="absolute top-20 right-0 w-64 h-64 bg-[#e8f5e9]/30 dark:bg-[#1b5e20]/10 rounded-full blur-3xl -z-10"></div>
        <div className="absolute bottom-40 left-0 w-80 h-80 bg-[#fff8e1]/30 dark:bg-[#ff9800]/10 rounded-full blur-3xl -z-10"></div>
      </section>

      {/* About Us Section */}
      <section ref={aboutRef} className={cn(containerClass, "py-24 relative z-10")} id="about">
        <motion.div
          initial="hidden"
          animate={aboutInView ? "visible" : "hidden"}
          variants={fadeIn}
          className="grid md:grid-cols-2 gap-16 items-center"
        >
          <div className="relative">
            <div className="relative z-10">
              <Card className="overflow-hidden border-none shadow-xl dark:shadow-gray-900/20 rounded-2xl">
                <CardContent className="p-0">
                  <Image
                    src="/images/mother-child.png"
                    alt="Mother and child representing WarmTouch Healthcare's family-centered approach"
                    width={500}
                    height={600}
                    className="object-cover w-full h-full"
                  />
                </CardContent>
              </Card>
            </div>

            <div className="absolute -bottom-8 -left-8 bg-white/90 dark:bg-gray-800/90 backdrop-blur-md p-6 rounded-xl shadow-lg border border-gray-100 dark:border-gray-700 max-w-xs z-20">
              <div className="flex items-start gap-3">
                <div className="mt-1">
                  <CheckCircle2 className="h-5 w-5 text-[#2e7d32] dark:text-[#4caf50]" />
                </div>
                <div>
                  <h4 className="text-lg font-medium text-gray-800 dark:text-gray-100 mb-1">Trusted by Families</h4>
                  <p className="text-gray-600 dark:text-gray-300 text-sm">
                    Our caregivers become like family to our clients, providing reliable and compassionate care.
                  </p>
                </div>
              </div>
            </div>

            <div className="absolute -z-10 top-10 -left-10 w-full h-full rounded-2xl border-2 border-[#ff9800]/20 dark:border-[#ff9800]/10"></div>

            <div className="absolute -z-20 top-20 -left-20 w-full h-full rounded-2xl border-2 border-[#2e7d32]/10 dark:border-[#2e7d32]/5"></div>
          </div>

          <div>
            <div className="inline-block">
              <Badge className="px-4 py-1.5 bg-[#fff8e1] dark:bg-[#ff9800]/20 text-[#ff9800] rounded-full text-sm font-medium mb-4">
                Who We Are
              </Badge>
            </div>

            <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-6">
              About WarmTouch Homecare Services
            </h2>

            <p className="text-gray-600 dark:text-gray-300 mb-6">
              At WarmTouch Homecare Services, we are dedicated to providing compassionate, high-quality care that
              allows individuals to live comfortably and independently in their own homes. Our approach is centered on
              respect, dignity, and personalized support, ensuring that every client receives the attention and
              assistance they deserve.
            </p>

            <p className="text-gray-600 dark:text-gray-300 mb-8">
              Our team of skilled and caring professionals is committed to delivering tailored in-home care solutions.
              Whether you need daily assistance, companionship, or specialized support, we understand that every
              individual has unique needs. That's why we customize our services to fit your lifestyle and preferences.
            </p>

            <p className="text-gray-600 dark:text-gray-300 mb-8">
              At WarmTouch Homecare Services, we believe that home is where healing, comfort, and cherished moments
              happen. That's why we go beyond just providing care—we create a warm, supportive environment where our
              clients and their families feel at ease.
            </p>

            <div className="space-y-4 mb-8">
              {[
                { icon: Users, text: "Compassionate and dedicated caregivers" },
                { icon: Shield, text: "Personalized care plans to fit your needs" },
                { icon: Clock, text: "Reliable, professional, and trustworthy service" },
                { icon: Heart, text: "Committed to enhancing quality of life" },
              ].map((item, index) => (
                <div key={index} className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-full bg-[#e8f5e9] dark:bg-[#1b5e20]/30 flex items-center justify-center">
                    <item.icon className="h-5 w-5 text-[#2e7d32] dark:text-[#4caf50]" />
                  </div>
                  <p className="text-gray-700 dark:text-gray-300">{item.text}</p>
                </div>
              ))}
            </div>

            <div>
              <Link href="/services">
                <Button className="rounded-full bg-[#ff9800] hover:bg-[#f57c00] text-white px-6 py-2 transition-all duration-300 hover:shadow-lg">
                  Explore Our Services
                </Button>
              </Link>
            </div>
          </div>
        </motion.div>
      </section>

      {/* Our Mission Section */}
      <section ref={missionRef} className="relative py-24 z-10 overflow-hidden">
        {/* Background with subtle pattern */}
        <div className="absolute inset-0 bg-[#f9f9f9] dark:bg-gray-900 z-0">
          <div className="absolute inset-0 opacity-5">
            <div className="absolute top-0 left-0 w-full h-full bg-[#2e7d32] dark:bg-[#1b5e20] rotate-45 transform origin-top-left scale-150 opacity-10"></div>
          </div>
        </div>

        <motion.div
          initial="hidden"
          animate={missionInView ? "visible" : "hidden"}
          variants={fadeIn}
          className={cn(containerClass, "relative z-10")}
        >
          <div className="text-center mb-16">
            <div className="inline-block">
              <Badge className="px-4 py-1.5 bg-[#e8f5e9] dark:bg-[#1b5e20]/30 text-[#2e7d32] dark:text-[#4caf50] rounded-full text-sm font-medium mb-4">
                Our Purpose
              </Badge>
            </div>
            <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-4">Our Mission</h2>
            <div className="w-20 h-1 bg-[#ff9800] mx-auto rounded-full mb-6"></div>
            <p className="max-w-2xl mx-auto text-gray-600 dark:text-gray-300 text-lg font-light">
              At WarmTouch Homecare Services, we are dedicated to enhancing the quality of life for our clients by
              providing compassionate, personalized care that promotes independence, dignity, and well-being in the
              comfort of their own homes.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                icon: HeartHandshake,
                title: "Compassionate Care",
                description:
                  "We provide care with kindness, empathy, and respect, treating each client as a unique individual with specific needs and preferences.",
                color: "#2e7d32",
              },
              {
                icon: Award,
                title: "Excellence in Service",
                description:
                  "We are committed to maintaining the highest standards of professional care through continuous training and quality improvement.",
                color: "#ff9800",
              },
              {
                icon: Smile,
                title: "Enhancing Well-being",
                description:
                  "We focus on improving our clients' overall quality of life by addressing physical, emotional, and social needs.",
                color: "#757575",
              },
            ].map((item, index) => (
              <div
                key={index}
                className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm p-6 rounded-xl shadow-md border border-gray-100 dark:border-gray-700 hover:shadow-lg transition-all duration-300 hover:-translate-y-1"
              >
                <div className="flex flex-col items-center text-center">
                  <div
                    className="w-16 h-16 rounded-full flex items-center justify-center mb-4"
                    style={{ backgroundColor: `${item.color}10` }}
                  >
                    <item.icon className="h-8 w-8" style={{ color: item.color }} />
                  </div>
                  <h3 className="text-xl font-medium text-gray-800 dark:text-gray-100 mb-3">{item.title}</h3>
                  <p className="text-gray-600 dark:text-gray-300 font-light">{item.description}</p>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-16 text-center">
            <Link href="/consultation">
              <Button className="rounded-full bg-[#2e7d32] hover:bg-[#1b5e20] text-white px-6 py-6 h-auto transition-all duration-300 hover:shadow-lg">
                Book a Consultation
              </Button>
            </Link>
          </div>
        </motion.div>
      </section>

      {/* Contact Form Section */}
      <section
        ref={contactRef}
        className="py-24 relative z-10 overflow-hidden bg-gradient-to-b from-white to-[#f9f9f9] dark:from-gray-950 dark:to-gray-900"
      >
        <motion.div
          initial="hidden"
          animate={contactInView ? "visible" : "hidden"}
          variants={fadeIn}
          className={cn(containerClass, "relative z-10")}
        >
          <div className="text-center mb-16">
            <div className="inline-block">
              <Badge className="px-4 py-1.5 bg-[#e8f5e9] dark:bg-[#1b5e20]/30 text-[#2e7d32] dark:text-[#4caf50] rounded-full text-sm font-medium mb-4">
                Get In Touch
              </Badge>
            </div>
            <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-4">Contact Us</h2>
            <div className="w-20 h-1 bg-[#ff9800] mx-auto rounded-full mb-6"></div>
            <p className="max-w-2xl mx-auto text-gray-600 dark:text-gray-300 text-lg font-light">
              Have questions or ready to discuss your care needs? We're here to help you find the right solutions for
              your situation.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-12 items-start">
            <div className="space-y-8">
              <h3 className="text-2xl font-light text-gray-800 dark:text-gray-100">Send Us a Message</h3>

              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label htmlFor="name" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Your Name
                    </label>
                    <Input
                      id="name"
                      placeholder="John Doe"
                      className="rounded-lg border-gray-200 dark:border-gray-700 focus:border-[#2e7d32] dark:focus:border-[#4caf50] focus:ring-[#2e7d32]/10 dark:focus:ring-[#4caf50]/10 transition-all duration-300 dark:bg-gray-800 dark:text-gray-100"
                    />
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="email" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Email Address
                    </label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="<EMAIL>"
                      className="rounded-lg border-gray-200 dark:border-gray-700 focus:border-[#2e7d32] dark:focus:border-[#4caf50] focus:ring-[#2e7d32]/10 dark:focus:ring-[#4caf50]/10 transition-all duration-300 dark:bg-gray-800 dark:text-gray-100"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <label htmlFor="phone" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Phone Number
                  </label>
                  <Input
                    id="phone"
                    placeholder="(*************"
                    className="rounded-lg border-gray-200 dark:border-gray-700 focus:border-[#2e7d32] dark:focus:border-[#4caf50] focus:ring-[#2e7d32]/10 dark:focus:ring-[#4caf50]/10 transition-all duration-300 dark:bg-gray-800 dark:text-gray-100"
                  />
                </div>

                <div className="space-y-2">
                  <label htmlFor="service" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Service of Interest
                  </label>
                  <select
                    id="service"
                    className="w-full rounded-lg border-gray-200 dark:border-gray-700 focus:border-[#2e7d32] dark:focus:border-[#4caf50] focus:ring-[#2e7d32]/10 dark:focus:ring-[#4caf50]/10 transition-all duration-300 dark:bg-gray-800 dark:text-gray-100"
                  >
                    <option value="">Select a service</option>
                    {services.map((service, index) => (
                      <option key={index} value={service.title}>
                        {service.title}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="space-y-2">
                  <label htmlFor="message" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Your Message
                  </label>
                  <Textarea
                    id="message"
                    placeholder="Tell us about your care needs..."
                    className="min-h-[120px] rounded-lg border-gray-200 dark:border-gray-700 focus:border-[#2e7d32] dark:focus:border-[#4caf50] focus:ring-[#2e7d32]/10 dark:focus:ring-[#4caf50]/10 transition-all duration-300 dark:bg-gray-800 dark:text-gray-100"
                  />
                </div>

                <div className="pt-2">
                  <Button className="w-full rounded-lg bg-[#ff9800] hover:bg-[#f57c00] text-white py-6 h-auto transition-all duration-300 hover:shadow-lg flex items-center justify-center gap-2 group">
                    <span>Send Message</span>
                    <Send className="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
                  </Button>
                </div>
              </div>
            </div>

            <div className="space-y-8">
              <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm p-8 rounded-xl shadow-lg border border-gray-100 dark:border-gray-700">
                <h3 className="text-2xl font-light text-gray-800 dark:text-gray-100 mb-6">Contact Information</h3>

                <div className="space-y-6">
                  {contactInfo.map((contact, index) => (
                    <div key={index} className="flex items-start gap-4">
                      <div className="w-10 h-10 rounded-full bg-[#e8f5e9] dark:bg-[#1b5e20]/30 flex items-center justify-center flex-shrink-0">
                        <contact.icon className="h-5 w-5 text-[#2e7d32] dark:text-[#4caf50]" />
                      </div>
                      <div>
                        <h4 className="text-base font-medium text-gray-800 dark:text-gray-100">{contact.label}</h4>
                        <p className="text-gray-600 dark:text-gray-300">{contact.value}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="rounded-xl overflow-hidden shadow-lg h-[300px] relative">
                <iframe
                  src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3360.8188962977776!2d-97.1419487!3d32.55673!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x864e6e6f6823d01d%3A0x7c9fb8a7929f83d3!2s309%20Dover%20Heights%20Trl%2C%20Mansfield%2C%20TX%2076063!5e0!3m2!1sen!2sus!4v1648138000000!5m2!1sen!2sus"
                  width="100%"
                  height="100%"
                  style={{ border: 0 }}
                  allowFullScreen
                  loading="lazy"
                  referrerPolicy="no-referrer-when-downgrade"
                  title="WarmTouch Healthcare Location"
                  className="absolute inset-0"
                ></iframe>
              </div>

              <div className="bg-[#e8f5e9]/50 dark:bg-[#1b5e20]/20 p-6 rounded-xl border border-[#2e7d32]/10 dark:border-[#4caf50]/10">
                <h3 className="text-lg font-medium text-[#2e7d32] dark:text-[#4caf50] mb-3">Office Hours</h3>
                <ul className="space-y-2 text-gray-700 dark:text-gray-300">
                  <li className="flex justify-between">
                    <span>Monday - Friday:</span>
                    <span>8:00 AM - 5:00 PM</span>
                  </li>
                  <li className="flex justify-between">
                    <span>Saturday:</span>
                    <span>Closed</span>
                  </li>
                  <li className="flex justify-between">
                    <span>Sunday:</span>
                    <span>Closed</span>
                  </li>
                </ul>
                <p className="mt-4 text-sm text-gray-600 dark:text-gray-400">
                  * 24/7 emergency support available for existing clients
                </p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Decorative elements */}
        <div className="absolute top-40 right-10 w-64 h-64 bg-[#e8f5e9]/30 dark:bg-[#1b5e20]/10 rounded-full blur-3xl -z-10"></div>
        <div className="absolute bottom-20 left-10 w-80 h-80 bg-[#fff8e1]/30 dark:bg-[#ff9800]/10 rounded-full blur-3xl -z-10"></div>
      </section>

      {/* FAQ Section */}
      <section
        ref={faqRef}
        className="py-24 relative z-10 overflow-hidden bg-gradient-to-b from-[#f9f9f9] to-white dark:from-gray-900 dark:to-gray-950"
      >
        <motion.div
          initial="hidden"
          animate={faqInView ? "visible" : "hidden"}
          variants={fadeIn}
          className={cn(containerClass, "relative z-10")}
        >
          <div className="text-center mb-16">
            <div className="inline-block">
              <Badge className="px-4 py-1.5 bg-[#fff8e1] dark:bg-[#ff9800]/20 text-[#ff9800] rounded-full text-sm font-medium mb-4">
                Questions & Answers
              </Badge>
            </div>
            <h2 className="text-3xl md:text-4xl font-light text-gray-800 dark:text-gray-100 mb-4">
              Frequently Asked Questions
            </h2>
            <div className="w-20 h-1 bg-[#2e7d32] dark:bg-[#4caf50] mx-auto rounded-full mb-6"></div>
            <p className="max-w-2xl mx-auto text-gray-600 dark:text-gray-300 text-lg font-light">
              Find answers to common questions about our services, caregivers, and care process. If you don't see your
              question here, please contact us.
            </p>
          </div>

          <div className="max-w-3xl mx-auto">
            <Accordion type="single" collapsible className="space-y-4">
              {faqs.map((faq, index) => (
                <div key={index}>
                  <AccordionItem
                    value={`item-${index}`}
                    className="border border-gray-200 dark:border-gray-700 rounded-xl overflow-hidden shadow-sm bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm"
                  >
                    <AccordionTrigger className="px-6 py-4 hover:no-underline text-gray-800 dark:text-gray-100 font-medium text-left data-[state=open]:text-[#2e7d32] dark:data-[state=open]:text-[#4caf50]">
                      {faq.question}
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-4 text-gray-600 dark:text-gray-300">
                      {faq.answer}
                    </AccordionContent>
                  </AccordionItem>
                </div>
              ))}
            </Accordion>
          </div>

          <div className="mt-12 text-center">
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Still have questions? We're here to help you get the answers you need.
            </p>
            <Link href="/contact">
              <Button className="rounded-full bg-[#ff9800] hover:bg-[#f57c00] text-white px-6 py-2 transition-all duration-300 hover:shadow-lg">
                Contact Our Support Team
              </Button>
            </Link>
          </div>
        </motion.div>

        {/* Decorative elements */}
        <div className="absolute top-20 right-20 w-64 h-64 bg-[#e8f5e9]/30 dark:bg-[#1b5e20]/10 rounded-full blur-3xl -z-10"></div>
        <div className="absolute bottom-40 left-20 w-80 h-80 bg-[#fff8e1]/30 dark:bg-[#ff9800]/10 rounded-full blur-3xl -z-10"></div>
      </section>

      <Footer />
    </div>
  )
}
