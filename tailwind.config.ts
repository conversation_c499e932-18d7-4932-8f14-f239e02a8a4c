import type { Config } from "tailwindcss"

const config: Config = {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
    "*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ["var(--font-nunito-sans)", "system-ui", "sans-serif"],
      },
      colors: {
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        primary: {
          DEFAULT: "hsl(var(--primary))", // Lavender
          foreground: "hsl(var(--primary-foreground))",
          light: "hsl(240, 60%, 85%)", // Lighter lavender
          dark: "hsl(240, 50%, 65%)", // Darker lavender
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))", // Blush
          foreground: "hsl(var(--secondary-foreground))",
          light: "hsl(350, 85%, 80%)", // Lighter blush
          dark: "hsl(350, 75%, 65%)", // Darker blush
        },
        // Journey of Care accent colors from logo
        sage: {
          DEFAULT: "#B8C5A7", // Sage green from hands
          light: "#D4E0C9", // Lighter sage
          dark: "#9CAA85", // Darker sage
        },
        peach: {
          DEFAULT: "#E8B4A0", // Warm peach from sun
          light: "#F2D0C4", // Light peach
          dark: "#D4967C", // Darker peach
        },
        // Legacy colors for compatibility
        lavender: {
          DEFAULT: "hsl(240, 50%, 80%)", // True lavender
          light: "hsl(240, 60%, 85%)", // Lighter lavender
          dark: "hsl(240, 50%, 65%)", // Darker lavender
        },
        pink: {
          DEFAULT: "hsl(350, 80%, 75%)", // True blush/pink
          light: "hsl(350, 85%, 80%)", // Light blush
          dark: "hsl(350, 75%, 65%)", // Darker blush
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        chart: {
          "1": "hsl(var(--chart-1))",
          "2": "hsl(var(--chart-2))",
          "3": "hsl(var(--chart-3))",
          "4": "hsl(var(--chart-4))",
          "5": "hsl(var(--chart-5))",
        },
        sidebar: {
          DEFAULT: "hsl(var(--sidebar-background))",
          foreground: "hsl(var(--sidebar-foreground))",
          primary: "hsl(var(--sidebar-primary))",
          "primary-foreground": "hsl(var(--sidebar-primary-foreground))",
          accent: "hsl(var(--sidebar-accent))",
          "accent-foreground": "hsl(var(--sidebar-accent-foreground))",
          border: "hsl(var(--sidebar-border))",
          ring: "hsl(var(--sidebar-ring))",
        },
        neutral: {
          DEFAULT: "#6b7280", // Gray
          light: "#9ca3af", // Lighter gray
          dark: "#4b5563", // Darker gray
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: {
            height: "0",
          },
          to: {
            height: "var(--radix-accordion-content-height)",
          },
        },
        "accordion-up": {
          from: {
            height: "var(--radix-accordion-content-height)",
          },
          to: {
            height: "0",
          },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
}
export default config
