"use client"

import type React from "react"

import { useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  Heart,
  MapPin,
  Phone,
  Mail,
  Clock,
  Facebook,
  Instagram,
  Youtube,
  Send,
  ArrowRight,
  Award,
  Users,
  Shield,
  ChevronRight,
  ExternalLink,
} from "lucide-react"
import { useInView } from "react-intersection-observer"

// TikTok icon component
function Tiktok({ className = "h-5 w-5" }) {
  return (
    <svg className={className} viewBox="0 0 48 48" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
      <path d="M33.6,14.5c-2-1.4-3.3-3.6-3.4-6.2h-5.7v23.6c0,2.4-1.9,4.3-4.3,4.3S16,34.3,16,31.9s1.9-4.3,4.3-4.3c0.6,0,1.1,0.1,1.6,0.3v-6c-0.5-0.1-1-0.1-1.6-0.1c-5.7,0-10.3,4.6-10.3,10.3S14.7,42,20.3,42s10.3-4.6,10.3-10.3v-9.6c1.8,1.3,4.1,2.1,6.5,2.1V18C36.4,18,34.8,16.5,33.6,14.5z" />
    </svg>
  )
}

export default function Footer() {
  const [email, setEmail] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitMessage, setSubmitMessage] = useState("")
  const { ref, inView } = useInView({ triggerOnce: true, threshold: 0.1 })

  // Animation variants
  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.6 } },
  }

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { staggerChildren: 0.1 } },
  }

  // Handle newsletter signup
  const handleNewsletterSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      const response = await fetch("/api/subscribe", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email }),
      })

      if (response.ok) {
        setSubmitMessage("Thank you for subscribing!")
        setEmail("")
      } else {
        setSubmitMessage("Something went wrong. Please try again.")
      }
    } catch (error) {
      setSubmitMessage("Something went wrong. Please try again.")
    } finally {
      setIsSubmitting(false)
      setTimeout(() => setSubmitMessage(""), 3000)
    }
  }

  // Custom container class
  const containerClass = "px-4 md:px-8 lg:px-16 xl:px-24 2xl:px-32 mx-auto max-w-[1600px]"

  // Updated services for Journey of Care
  const services = [
    { name: "Inclusive Care for All Abilities", href: "/services?service=inclusive-care" },
    { name: "Personal Care Services", href: "/services?service=personal-care" },
    { name: "Companion Care", href: "/services?service=companion-care" },
    { name: "Respite Care", href: "/services?service=respite-care" },
    { name: "In-Facility Care", href: "/services?service=in-facility-care" },
  ]

  // Updated quick links
  const quickLinks = [
    { name: "About Us", href: "/about" },
    { name: "Our Services", href: "/services" },
    { name: "Careers", href: "/careers" },
    { name: "Blog", href: "/blog" },
    { name: "Contact", href: "/contact" },
    { name: "Refer Us", href: "/refer" },
  ]

  // Legal links
  const legalLinks = [
    { name: "Privacy Policy", href: "/privacy-policy" },
    { name: "Terms & Conditions", href: "/terms-conditions" },
  ]

  // Updated social media links for Journey of Care
  const socialLinks = [
    {
      name: "Facebook",
      href: "https://www.facebook.com/share/1WRoiiWmHT/?mibextid=wwXIfr",
      icon: Facebook,
      color: "#1877F2",
    },
    {
      name: "Instagram",
      href: "https://www.instagram.com/journeyofcare?igsh=MWFwZHlxMmltMDZtYQ%3D%3D&utm_source=qr",
      icon: Instagram,
      color: "#E1306C",
    }
  ]

  // Updated contact information for Journey of Care
  const contactInfo = [
    {
      icon: MapPin,
      label: "Service Area",
      value: "North Houston, Conroe, TheWoodlands, Spring, and surrounding areas",
      href: "https://maps.google.com/?q=Conroe,+TX",
    },
    {
      icon: Phone,
      label: "Phone",
      value: "(*************",
      href: "tel:+***********",
    },
    {
      icon: Mail,
      label: "Email",
      value: "<EMAIL>",
      href: "mailto:<EMAIL>",
    },
  ]

  // Business hours
  const businessHours = [
    { day: "Monday - Friday", hours: "8:00 AM – 5:00 PM", icon: "🕘" },
    { day: "Saturday & Sunday", hours: "On-call availability", icon: "📞" },
    { day: "24/7 Emergency", hours: "Available when needed", icon: "🚨" },
  ]

  return (
    <footer className="relative bg-gradient-to-b from-white to-gray-50 dark:from-gray-950 dark:to-gray-900 border-t border-gray-100 dark:border-gray-800">
      {/* Decorative background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-96 h-96 rounded-full bg-gradient-to-br from-primary/5 to-primary-light/2 dark:from-primary/3 dark:to-primary-light/1 blur-3xl"></div>
        <div className="absolute top-1/3 -left-40 w-80 h-80 rounded-full bg-gradient-to-tr from-secondary/5 to-secondary-light/2 dark:from-secondary/3 dark:to-secondary-light/1 blur-3xl"></div>
      </div>

      <motion.div
        ref={ref}
        initial="hidden"
        animate={inView ? "visible" : "hidden"}
        variants={staggerContainer}
        className="relative z-10"
      >
        {/* Main Footer Content */}
        <div className={`${containerClass} py-16`}>
          <div className="grid lg:grid-cols-4 md:grid-cols-2 gap-8">
            {/* Company Info with Logo */}
            <motion.div variants={fadeInUp} className="lg:col-span-1">
              <div className="mb-6">
                {/* Logo */}
                <Link href="/" className="flex items-center mb-4">
                  <Image
                    src="/h.png"
                    alt="Journey of Care Logo"
                    width={200}
                    height={80}
                    className="h-auto w-auto max-h-12"
                  />
                </Link>
               
                <p className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed">
                  Providing trustworthy, heartfelt home care services in North Houston, Conroe, The
Woodlands, Spring, and surrounding areas
                  Bringing compassionate support to your doorstep with certified caregivers who offer steady hands and
                  open hearts.
                </p>
              </div>

              {/* Trust Badges */}
              <div className="space-y-3 mb-6">
                <div className="flex items-center gap-2">
                  <Shield className="h-4 w-4 text-primary" />
                  <span className="text-sm text-gray-600 dark:text-gray-300">Licensed & Insured</span>
                </div>
                <div className="flex items-center gap-2">
                  <Award className="h-4 w-4 text-primary" />
                  <span className="text-sm text-gray-600 dark:text-gray-300">Certified Caregivers</span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-primary" />
                  <span className="text-sm text-gray-600 dark:text-gray-300">Personalized Care Plans</span>
                </div>
              </div>

              {/* Social Media */}
              <div className="flex gap-3">
                {socialLinks.map((social) => (
                  <a
                    key={social.name}
                    href={social.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-9 h-9 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center text-gray-600 dark:text-gray-300 hover:bg-primary hover:text-white transition-all duration-300 hover:scale-110"
                    aria-label={`Follow us on ${social.name}`}
                  >
                    <social.icon className="h-4 w-4" />
                  </a>
                ))}
              </div>
            </motion.div>

            {/* Services */}
            <motion.div variants={fadeInUp}>
              <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-4">Our Services</h3>
              <ul className="space-y-2">
                {services.map((service) => (
                  <li key={service.name}>
                    <Link
                      href={service.href}
                      className="text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary-light transition-colors duration-200 text-sm flex items-center group"
                    >
                      <ChevronRight className="h-3 w-3 mr-1 transition-transform duration-200 group-hover:translate-x-1" />
                      {service.name}
                    </Link>
                  </li>
                ))}
              </ul>

              <div className="mt-6">
                <Link href="/consultation">
                  <Button className="rounded-full bg-gradient-to-r from-pink to-lavender hover:from-pink-dark hover:to-lavender-dark text-white px-4 py-2 text-sm transition-all duration-300 hover:shadow-md flex items-center gap-2 group">
                    <span>Schedule Assessment</span>
                    <ArrowRight className="h-3 w-3 transition-transform duration-300 group-hover:translate-x-1" />
                  </Button>
                </Link>
              </div>
            </motion.div>

            {/* Quick Links */}
            <motion.div variants={fadeInUp}>
              <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-4">Quick Links</h3>
              <ul className="space-y-2">
                {quickLinks.map((link) => (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      className="text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary-light transition-colors duration-200 text-sm flex items-center group"
                    >
                      <ChevronRight className="h-3 w-3 mr-1 transition-transform duration-200 group-hover:translate-x-1" />
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>

              <div className="mt-6">
                <h4 className="text-sm font-medium text-gray-800 dark:text-gray-100 mb-2">Legal</h4>
                <ul className="space-y-1">
                  {legalLinks.map((link) => (
                    <li key={link.name}>
                      <Link
                        href={link.href}
                        className="text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary-light transition-colors duration-200 text-xs"
                      >
                        {link.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            </motion.div>

            {/* Contact Info */}
            <motion.div variants={fadeInUp}>
              <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-4">Contact Information</h3>
              <div className="space-y-3">
                {contactInfo.map((info, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <info.icon className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide">{info.label}</p>
                      {info.href ? (
                        <a
                          href={info.href}
                          className="text-sm text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary-light transition-colors duration-200"
                          target={info.href.startsWith("http") ? "_blank" : undefined}
                          rel={info.href.startsWith("http") ? "noopener noreferrer" : undefined}
                        >
                          {info.value}
                          {info.href.startsWith("http") && <ExternalLink className="h-3 w-3 ml-1 inline" />}
                        </a>
                      ) : (
                        <p className="text-sm text-gray-600 dark:text-gray-300">{info.value}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              {/* Business Hours */}
              <div className="mt-6">
                <h4 className="text-sm font-medium text-gray-800 dark:text-gray-100 mb-3 flex items-center gap-2">
                  <Clock className="h-4 w-4 text-primary" />
                  Business Hours
                </h4>
                <div className="space-y-1">
                  {businessHours.map((schedule, index) => (
                    <div key={index} className="flex justify-between items-center text-xs">
                      <span className="text-gray-800 dark:text-gray-100 font-medium flex items-center gap-1">
                        <span>{schedule.icon}</span>
                        {schedule.day}:
                      </span>
                      <span className="text-gray-600 dark:text-gray-300">{schedule.hours}</span>
                    </div>
                  ))}
                </div>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Newsletter Section */}
        <motion.div variants={fadeInUp} className="border-t border-gray-100 dark:border-gray-800">
          <div className={`${containerClass} py-8`}>
            <div className="max-w-2xl mx-auto text-center">
              <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-100 mb-2">
                Stay Connected with Journey of Care
              </h3>
              <p className="text-gray-600 dark:text-gray-300 text-sm mb-6">
                Get home care tips, updates, and news delivered to your inbox.
              </p>

              <form onSubmit={handleNewsletterSubmit} className="flex gap-2 max-w-md mx-auto">
                <Input
                  type="email"
                  placeholder="Enter your email address"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  className="flex-1 rounded-full border-gray-200 dark:border-gray-700 focus:border-primary dark:focus:border-primary-light"
                />
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="rounded-full bg-primary hover:bg-primary-dark text-white px-6 transition-all duration-300 hover:shadow-md flex items-center gap-2"
                >
                  {isSubmitting ? (
                    "Subscribing..."
                  ) : (
                    <>
                      <Send className="h-4 w-4" />
                      Subscribe
                    </>
                  )}
                </Button>
              </form>

              {submitMessage && <p className="mt-3 text-sm text-primary dark:text-primary-light">{submitMessage}</p>}
            </div>
          </div>
        </motion.div>

        {/* Bottom Bar */}
        <motion.div variants={fadeInUp} className="border-t border-gray-100 dark:border-gray-800">
          <div className={`${containerClass} py-6`}>
            <div className="flex flex-col md:flex-row justify-between items-center gap-4">
              <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-300">
                <p>&copy; 2024 Journey of Care. All rights reserved.</p>
              </div>

              <div className="flex items-center gap-4">
                <Badge className="px-3 py-1 bg-primary/10 dark:bg-primary/20 text-primary dark:text-primary-light rounded-full text-xs">
                  Serving North Houston, Conroe, TheWoodlands, Spring, and surrounding areas
                </Badge>
                <div className="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400">
                  <Heart className="h-3 w-3 text-red-500" />
                  <span>Specializing in compassionate in-home senior care</span>
                </div>
              </div>
            </div>

            {/* SEO Footer Content */}
            <div className="mt-4 pt-4 border-t border-gray-100 dark:border-gray-800">
              <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
                Serving the areas of  North Houston, Conroe, TheWoodlands, Spring, and surrounding areasd surrounding communities.. Specializing in compassionate in-home senior care, including personal
                care, companion care, respite care, and specialized memory care.
              </p>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </footer>
  )
}
