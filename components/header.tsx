"use client"

import { useState, useEffect, useRef } from "react"
import Image from "next/image"
import Link from "next/link"
import { motion, AnimatePresence } from "framer-motion"
import { Menu, ChevronDown, Phone, X } from "lucide-react"
import { Button } from "@/components/ui/button"

// Update the services list for Journey of Care
const servicesList = [
  { title: "Inclusive Care for All Abilities", href: "/services?service=inclusive-care" },
  { title: "Personal Care Services", href: "/services?service=personal-care" },
  { title: "Companion Care", href: "/services?service=companion-care" },
  { title: "Respite Care", href: "/services?service=respite-care" },
  { title: "In-Facility Care", href: "/services?service=in-facility-care" },
]

export default function Header() {
  const [scrolled, setScrolled] = useState(false)
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const menuRef = useRef<HTMLDivElement>(null)
  const [servicesDropdownOpen, setServicesDropdownOpen] = useState(false)

  // Handle scroll effect for header
  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 20)
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  // Close menu when clicking outside
  useEffect(() => {
    if (!mobileMenuOpen) return

    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setMobileMenuOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    document.body.style.overflow = "hidden"

    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
      document.body.style.overflow = ""
    }
  }, [mobileMenuOpen])

  // Handle close button click
  const handleCloseMenu = () => {
    setMobileMenuOpen(false)
  }

  // Simplify animation variants
  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { staggerChildren: 0.1 } },
  }

  const navItem = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.3 } },
  }

  // Simplify mobile menu animations
  const menuVariants = {
    closed: { opacity: 0, x: "100%" },
    open: { opacity: 1, x: 0 },
  }

  const menuItemVariants = {
    closed: { opacity: 0 },
    open: { opacity: 1 },
  }

  // Navigation items - Updated for Journey of Care
  const navItems = [
    { name: "About", href: "/about" },
    {
      name: "Services",
      href: "/services",
      hasDropdown: true,
    },
    { name: "Careers", href: "/careers" },
    { name: "Blog", href: "/blog" },
    { name: "Refer Us", href: "/refer" },
    { name: "Contact", href: "/contact" },
  ]

  // Custom container class with increased margins on desktop
  const containerClass = "px-4 md:px-8 lg:px-16 xl:px-24 2xl:px-32 mx-auto max-w-[1600px]"

  // Add this effect to close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (servicesDropdownOpen && event.target instanceof Element && !event.target.closest(".relative.inline-block")) {
        setServicesDropdownOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [servicesDropdownOpen])

  // Add body class when dropdown is open to prevent scrollbar issues
  useEffect(() => {
    if (servicesDropdownOpen) {
      document.body.classList.add("dropdown-open")
    } else {
      document.body.classList.remove("dropdown-open")
    }

    return () => {
      document.body.classList.remove("dropdown-open")
    }
  }, [servicesDropdownOpen])

  return (
    <header
      className={`fixed top-0 left-0 right-0 z-[9000] transition-all duration-300 w-full ${
        scrolled ? "bg-white dark:bg-gray-950 shadow-md dark:shadow-gray-800/20" : "bg-white dark:bg-gray-950"
      }`}
    >
      {/* Top row: Logo, Phone Number, and CTA */}
      <div className={`${containerClass} py-3`}>
        <div className="flex justify-between items-center">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
            className="flex items-center gap-2"
          >
            <Link href="/" className="flex items-center">
              <motion.div
                initial={{ rotate: -10, opacity: 0 }}
                animate={{ rotate: 0, opacity: 1 }}
                transition={{ duration: 0.5 }}
                className="relative"
              >
                <Image
                  src="/h.png"
                  alt="Journey of Care Logo"
                  width={300}
                  height={100}
                  className="h-auto w-auto max-h-16"
                  priority
                />
              </motion.div>
            </Link>
          </motion.div>

          {/* Phone Number and CTA */}
          <div className="flex items-center gap-4">
            <a
              href="tel:8324460705"
              className="flex items-center text-primary font-medium hover:text-primary-dark transition-colors"
            >
              <Phone className="h-4 w-4 mr-2" />
              <span className="text-lg">(*************</span>
            </a>

            {/* Consultation CTA - Hidden on mobile */}
            <div className="hidden md:block">
              <Link href="/consultation">
                <Button className="rounded-full bg-gradient-to-r from-pink to-lavender hover:from-pink-dark hover:to-lavender-dark text-white px-6 py-2 h-auto transition-all duration-300 hover:shadow-lg">
                  Free Assessment
                </Button>
              </Link>
            </div>
          </div>

          {/* Mobile Menu Button - Show only on mobile */}
          <div className="md:hidden">
            <motion.button
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              onClick={() => setMobileMenuOpen(true)}
              className="w-9 h-9 rounded-full flex items-center justify-center bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200"
              aria-label="Open Menu"
            >
              <Menu className="h-4 w-4" />
            </motion.button>
          </div>
        </div>
      </div>

      {/* Bottom row: Navigation */}
      <div className="border-t border-gray-100 dark:border-gray-800 hidden md:block">
        <div className={`${containerClass} py-3`}>
          <div className="flex justify-center items-center">
            {/* Desktop Navigation */}
            <motion.nav
              variants={staggerContainer}
              initial="hidden"
              animate="visible"
              className="flex items-center gap-8"
            >
              {navItems.map((item) => (
                <motion.div key={item.name} variants={navItem}>
                  {item.hasDropdown ? (
                    <div className="relative inline-block dropdown-container">
                      <button
                        className="text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary-light text-[1.1rem] font-medium transition-colors duration-200 relative group flex items-center gap-1 focus:outline-none"
                        onClick={(e) => {
                          e.preventDefault()
                          e.stopPropagation()
                          setServicesDropdownOpen(!servicesDropdownOpen)
                        }}
                        aria-expanded={servicesDropdownOpen}
                        aria-haspopup="true"
                      >
                        {item.name}
                        <ChevronDown className="h-4 w-4" />
                        <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary dark:bg-primary-light transition-all duration-300 group-hover:w-full"></span>
                      </button>
                      {servicesDropdownOpen && (
                        <div
                          className="absolute left-0 mt-2 w-56 bg-white dark:bg-gray-800 shadow-xl rounded-lg p-2"
                          style={{
                            zIndex: 9999,
                            boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
                            border: "1px solid rgba(229, 231, 235, 0.5)",
                            maxWidth: "100vw",
                            overflow: "hidden",
                          }}
                        >
                          {servicesList.map((service, index) => (
                            <Link
                              key={index}
                              href={service.href}
                              className="block px-4 py-2 text-gray-800 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
                            >
                              {service.title}
                            </Link>
                          ))}
                        </div>
                      )}
                    </div>
                  ) : (
                    <Link
                      href={item.href}
                      className="text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary-light text-[1.1rem] font-medium transition-colors duration-200"
                    >
                      {item.name}
                    </Link>
                  )}
                </motion.div>
              ))}
            </motion.nav>
          </div>
        </div>
      </div>

      {/* Mobile Menu Overlay */}
      <AnimatePresence>
        {mobileMenuOpen && (
          <motion.div
            ref={menuRef}
            initial="closed"
            animate="open"
            exit="closed"
            variants={menuVariants}
            transition={{ duration: 0.3, ease: "easeInOut" }}
            className="fixed inset-0 z-[9999] bg-white dark:bg-gray-950 md:hidden"
            style={{ top: 0 }}
          >
            {/* Mobile Menu Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-100 dark:border-gray-800">
              <Link href="/" onClick={handleCloseMenu} className="flex items-center">
                <Image
                  src="/h.png"
                  alt="Journey of Care Logo"
                  width={200}
                  height={67}
                  className="h-auto w-auto max-h-12"
                  priority
                />
              </Link>
              <button
                onClick={handleCloseMenu}
                className="w-9 h-9 rounded-full flex items-center justify-center bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200"
                aria-label="Close Menu"
              >
                <X className="h-4 w-4" />
              </button>
            </div>

            {/* Mobile Menu Content */}
            <div className="flex flex-col h-full">
              <div className="flex-1 overflow-y-auto p-4">
                <motion.nav
                  variants={staggerContainer}
                  initial="hidden"
                  animate="visible"
                  className="space-y-1"
                >
                  {navItems.map((item, index) => (
                    <motion.div
                      key={item.name}
                      variants={menuItemVariants}
                      transition={{ delay: index * 0.1 }}
                    >
                      {item.hasDropdown ? (
                        <div className="space-y-1">
                          <Link
                            href={item.href}
                            onClick={handleCloseMenu}
                            className="block px-4 py-3 text-lg font-medium text-gray-800 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg transition-colors"
                          >
                            {item.name}
                          </Link>
                          <div className="ml-4 space-y-1">
                            {servicesList.map((service, serviceIndex) => (
                              <Link
                                key={serviceIndex}
                                href={service.href}
                                onClick={handleCloseMenu}
                                className="block px-4 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg transition-colors"
                              >
                                {service.title}
                              </Link>
                            ))}
                          </div>
                        </div>
                      ) : (
                        <Link
                          href={item.href}
                          onClick={handleCloseMenu}
                          className="block px-4 py-3 text-lg font-medium text-gray-800 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg transition-colors"
                        >
                          {item.name}
                        </Link>
                      )}
                    </motion.div>
                  ))}
                </motion.nav>
              </div>

              {/* Mobile Menu Footer */}
              <div className="p-4 border-t border-gray-100 dark:border-gray-800 space-y-4">
                {/* Phone Number */}
                <a
                  href="tel:8324460705"
                  className="flex items-center justify-center text-primary font-medium hover:text-primary-dark transition-colors"
                >
                  <Phone className="h-4 w-4 mr-2" />
                  <span className="text-lg">(*************</span>
                </a>

                {/* Free Assessment CTA */}
                <Link href="/consultation" onClick={handleCloseMenu}>
                  <Button className="w-full rounded-full bg-gradient-to-r from-pink to-lavender hover:from-pink-dark hover:to-lavender-dark text-white px-6 py-3 h-auto transition-all duration-300 hover:shadow-lg">
                    Free Assessment
                  </Button>
                </Link>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </header>
  )
}
